<?php
session_start();

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'includes/functions.php';

// تسجيل النشاط قبل تسجيل الخروج
if (isLoggedIn()) {
    logActivity('تسجيل خروج', 'تسجيل خروج من النظام', $_SESSION['user_id']);
    
    // حذف رمز التذكر من قاعدة البيانات
    try {
        $db = new Database();
        $conn = $db->getConnection();
        
        $stmt = $conn->prepare("UPDATE users SET remember_token = NULL WHERE id = ?");
        $stmt->execute([$_SESSION['user_id']]);
    } catch (Exception $e) {
        // تجاهل الخطأ
    }
}

// حذف جميع بيانات الجلسة
session_unset();
session_destroy();

// حذف ملف تعريف الارتباط للتذكر
if (isset($_COOKIE['remember_token'])) {
    setcookie('remember_token', '', time() - 3600, '/');
}

// إعادة التوجيه إلى صفحة تسجيل الدخول
header('Location: login.php?message=logout_success');
exit();
?>
