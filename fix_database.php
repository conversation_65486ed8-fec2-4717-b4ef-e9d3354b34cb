<?php
// ملف إصلاح مشاكل قاعدة البيانات

echo "<h2>إصلاح مشاكل قاعدة البيانات</h2>";

// الخطوة 1: فحص ملف الإعدادات
echo "<h3>الخطوة 1: فحص ملف الإعدادات</h3>";

if (!file_exists('config/config.php')) {
    echo "<p style='color: red;'>❌ ملف الإعدادات غير موجود</p>";
    echo "<p><a href='install.php'>انقر هنا لتشغيل التثبيت</a></p>";
    exit();
} else {
    echo "<p style='color: green;'>✅ ملف الإعدادات موجود</p>";
}

// تضمين ملف الإعدادات
require_once 'config/config.php';

// الخطوة 2: فحص الثوابت
echo "<h3>الخطوة 2: فحص ثوابت قاعدة البيانات</h3>";

$constants = ['DB_HOST', 'DB_NAME', 'DB_USER', 'DB_PASS'];
$missing_constants = [];

foreach ($constants as $constant) {
    if (defined($constant)) {
        $value = constant($constant);
        if ($constant === 'DB_PASS') {
            $display_value = $value ? 'محددة' : 'فارغة';
        } else {
            $display_value = $value ?: 'فارغ';
        }
        echo "<p style='color: green;'>✅ $constant: $display_value</p>";
    } else {
        echo "<p style='color: red;'>❌ $constant: غير محدد</p>";
        $missing_constants[] = $constant;
    }
}

if (!empty($missing_constants)) {
    echo "<p style='color: red;'><strong>يجب إعادة تشغيل التثبيت لإصلاح الثوابت المفقودة</strong></p>";
    echo "<p><a href='install.php'>انقر هنا لإعادة التثبيت</a></p>";
    exit();
}

// الخطوة 3: فحص الاتصال بقاعدة البيانات
echo "<h3>الخطوة 3: فحص الاتصال بقاعدة البيانات</h3>";

try {
    $dsn = "mysql:host=" . DB_HOST . ";charset=utf8mb4";
    $pdo = new PDO($dsn, DB_USER, DB_PASS, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
    echo "<p style='color: green;'>✅ تم الاتصال بخادم MySQL بنجاح</p>";
    
    // فحص وجود قاعدة البيانات
    $stmt = $pdo->prepare("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?");
    $stmt->execute([DB_NAME]);
    $database_exists = $stmt->fetch();
    
    if ($database_exists) {
        echo "<p style='color: green;'>✅ قاعدة البيانات " . DB_NAME . " موجودة</p>";
        
        // الاتصال بقاعدة البيانات المحددة
        $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4";
        $pdo = new PDO($dsn, DB_USER, DB_PASS, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);
        
        echo "<p style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
        
        // فحص الجداول المطلوبة
        echo "<h3>الخطوة 4: فحص الجداول</h3>";
        
        $required_tables = [
            'users', 'customers', 'categories', 'products', 'sales', 
            'sale_items', 'maintenance_requests', 'settings', 'notifications', 
            'activity_logs'
        ];
        
        $stmt = $pdo->query("SHOW TABLES");
        $existing_tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $missing_tables = [];
        foreach ($required_tables as $table) {
            if (in_array($table, $existing_tables)) {
                echo "<p style='color: green;'>✅ جدول $table موجود</p>";
            } else {
                echo "<p style='color: red;'>❌ جدول $table مفقود</p>";
                $missing_tables[] = $table;
            }
        }
        
        if (!empty($missing_tables)) {
            echo "<p style='color: red;'><strong>يجب إعادة تشغيل التثبيت لإنشاء الجداول المفقودة</strong></p>";
            echo "<p><a href='install.php'>انقر هنا لإعادة التثبيت</a></p>";
        } else {
            echo "<h3>الخطوة 5: فحص البيانات</h3>";
            
            // فحص المستخدمين
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
            $user_count = $stmt->fetch()['count'];
            
            if ($user_count > 0) {
                echo "<p style='color: green;'>✅ يوجد $user_count مستخدم في النظام</p>";
                echo "<p style='color: green;'><strong>النظام جاهز للاستخدام!</strong></p>";
                echo "<p><a href='login.php' style='background: green; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تسجيل الدخول</a></p>";
            } else {
                echo "<p style='color: red;'>❌ لا يوجد مستخدمين في النظام</p>";
                echo "<p><a href='install.php'>انقر هنا لإنشاء مستخدم مدير</a></p>";
            }
        }
        
    } else {
        echo "<p style='color: red;'>❌ قاعدة البيانات " . DB_NAME . " غير موجودة</p>";
        
        // محاولة إنشاء قاعدة البيانات
        echo "<h3>محاولة إنشاء قاعدة البيانات</h3>";
        try {
            $pdo->exec("CREATE DATABASE `" . DB_NAME . "` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            echo "<p style='color: green;'>✅ تم إنشاء قاعدة البيانات بنجاح</p>";
            echo "<p><a href='install.php'>انقر هنا لإكمال التثبيت</a></p>";
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ فشل في إنشاء قاعدة البيانات: " . $e->getMessage() . "</p>";
            echo "<p>يرجى إنشاء قاعدة البيانات يدوياً أو التأكد من صلاحيات المستخدم</p>";
        }
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ فشل الاتصال بقاعدة البيانات</p>";
    echo "<p style='color: red;'>الخطأ: " . $e->getMessage() . "</p>";
    
    echo "<h3>الحلول المقترحة:</h3>";
    echo "<ol>";
    echo "<li>تأكد من تشغيل خادم MySQL</li>";
    echo "<li>تأكد من صحة اسم المستخدم وكلمة المرور</li>";
    echo "<li>تأكد من صلاحيات المستخدم</li>";
    echo "<li><a href='install.php'>إعادة تشغيل التثبيت</a></li>";
    echo "</ol>";
}

?>

<style>
body {
    font-family: Arial, sans-serif;
    direction: rtl;
    text-align: right;
    margin: 20px;
    background: #f5f5f5;
    line-height: 1.6;
}

h2, h3 {
    color: #333;
    border-bottom: 2px solid #667eea;
    padding-bottom: 10px;
}

p {
    background: white;
    padding: 10px;
    border-radius: 5px;
    margin: 10px 0;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

a {
    color: #667eea;
    text-decoration: none;
    padding: 10px 15px;
    background: white;
    border-radius: 5px;
    margin: 0 5px;
    display: inline-block;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

a:hover {
    background: #667eea;
    color: white;
}

ol {
    background: white;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

li {
    margin: 10px 0;
}
</style>
