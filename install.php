<?php
// ملف تثبيت النظام
session_start();

// التحقق من وجود ملف الإعدادات
if (file_exists('config/config.php')) {
    header('Location: login.php');
    exit();
}

$step = isset($_GET['step']) ? (int)$_GET['step'] : 1;
$error = '';
$success = '';

// معالجة النماذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($step == 1) {
        // التحقق من بيانات قاعدة البيانات
        $db_host = trim($_POST['db_host']);
        $db_name = trim($_POST['db_name']);
        $db_user = trim($_POST['db_user']);
        $db_pass = trim($_POST['db_pass']);
        
        if (empty($db_host) || empty($db_name) || empty($db_user)) {
            $error = 'جميع الحقول مطلوبة';
        } else {
            // اختبار الاتصال بقاعدة البيانات
            try {
                $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8", $db_user, $db_pass);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                // حفظ بيانات قاعدة البيانات في الجلسة
                $_SESSION['db_config'] = [
                    'host' => $db_host,
                    'name' => $db_name,
                    'user' => $db_user,
                    'pass' => $db_pass
                ];
                
                header('Location: install.php?step=2');
                exit();
            } catch (PDOException $e) {
                $error = 'فشل الاتصال بقاعدة البيانات: ' . $e->getMessage();
            }
        }
    } elseif ($step == 2) {
        // إنشاء حساب الأدمن
        $admin_username = trim($_POST['admin_username']);
        $admin_email = trim($_POST['admin_email']);
        $admin_password = trim($_POST['admin_password']);
        $confirm_password = trim($_POST['confirm_password']);
        
        if (empty($admin_username) || empty($admin_email) || empty($admin_password)) {
            $error = 'جميع الحقول مطلوبة';
        } elseif ($admin_password !== $confirm_password) {
            $error = 'كلمات المرور غير متطابقة';
        } elseif (strlen($admin_password) < 6) {
            $error = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
        } else {
            // حفظ بيانات الأدمن في الجلسة
            $_SESSION['admin_config'] = [
                'username' => $admin_username,
                'email' => $admin_email,
                'password' => password_hash($admin_password, PASSWORD_DEFAULT)
            ];
            
            header('Location: install.php?step=3');
            exit();
        }
    } elseif ($step == 3) {
        // إنشاء قاعدة البيانات والجداول
        try {
            $db_config = $_SESSION['db_config'];
            $admin_config = $_SESSION['admin_config'];
            
            $pdo = new PDO("mysql:host={$db_config['host']};dbname={$db_config['name']};charset=utf8", 
                          $db_config['user'], $db_config['pass']);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // إنشاء الجداول
            createTables($pdo);
            
            // إنشاء حساب الأدمن
            $stmt = $pdo->prepare("INSERT INTO users (username, email, password, role, is_active, created_at) VALUES (?, ?, ?, 'admin', 1, NOW())");
            $stmt->execute([$admin_config['username'], $admin_config['email'], $admin_config['password']]);
            
            // إنشاء ملف الإعدادات
            createConfigFile($db_config);
            
            // إنشاء المجلدات المطلوبة
            createDirectories();
            
            // تنظيف الجلسة
            unset($_SESSION['db_config']);
            unset($_SESSION['admin_config']);
            
            $success = 'تم تثبيت النظام بنجاح!';
            
        } catch (Exception $e) {
            $error = 'خطأ في التثبيت: ' . $e->getMessage();
        }
    }
}

// دالة إنشاء الجداول
function createTables($pdo) {
    $tables = [
        // جدول المستخدمين
        "CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            role ENUM('admin', 'user') DEFAULT 'user',
            is_active BOOLEAN DEFAULT 1,
            last_login DATETIME NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )",
        
        // جدول العملاء
        "CREATE TABLE IF NOT EXISTS customers (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            phone VARCHAR(20),
            email VARCHAR(100),
            address TEXT,
            balance DECIMAL(10,2) DEFAULT 0.00,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )",
        
        // جدول الفئات
        "CREATE TABLE IF NOT EXISTS categories (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )",
        
        // جدول المنتجات
        "CREATE TABLE IF NOT EXISTS products (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(200) NOT NULL,
            description TEXT,
            barcode VARCHAR(50) UNIQUE,
            category_id INT,
            purchase_price DECIMAL(10,2) NOT NULL,
            selling_price DECIMAL(10,2) NOT NULL,
            quantity INT DEFAULT 0,
            min_quantity INT DEFAULT 5,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (category_id) REFERENCES categories(id)
        )",
        
        // جدول المبيعات
        "CREATE TABLE IF NOT EXISTS sales (
            id INT AUTO_INCREMENT PRIMARY KEY,
            invoice_number VARCHAR(50) UNIQUE NOT NULL,
            customer_id INT,
            user_id INT NOT NULL,
            total_amount DECIMAL(10,2) NOT NULL,
            paid_amount DECIMAL(10,2) DEFAULT 0.00,
            discount DECIMAL(10,2) DEFAULT 0.00,
            status ENUM('completed', 'pending', 'cancelled') DEFAULT 'completed',
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (customer_id) REFERENCES customers(id),
            FOREIGN KEY (user_id) REFERENCES users(id)
        )",
        
        // جدول تفاصيل المبيعات
        "CREATE TABLE IF NOT EXISTS sale_items (
            id INT AUTO_INCREMENT PRIMARY KEY,
            sale_id INT NOT NULL,
            product_id INT NOT NULL,
            quantity INT NOT NULL,
            unit_price DECIMAL(10,2) NOT NULL,
            total_price DECIMAL(10,2) NOT NULL,
            FOREIGN KEY (sale_id) REFERENCES sales(id) ON DELETE CASCADE,
            FOREIGN KEY (product_id) REFERENCES products(id)
        )",
        
        // جدول طلبات الصيانة
        "CREATE TABLE IF NOT EXISTS maintenance_requests (
            id INT AUTO_INCREMENT PRIMARY KEY,
            maintenance_number VARCHAR(50) UNIQUE NOT NULL,
            customer_id INT,
            device_name VARCHAR(100) NOT NULL,
            device_details TEXT,
            problem_description TEXT NOT NULL,
            estimated_cost DECIMAL(10,2),
            actual_cost DECIMAL(10,2),
            status ENUM('pending', 'in_progress', 'completed', 'failed', 'delivered') DEFAULT 'pending',
            failure_reason TEXT,
            spare_parts_needed TEXT,
            delivered_to VARCHAR(100),
            paid_amount DECIMAL(10,2) DEFAULT 0.00,
            payment_status ENUM('unpaid', 'partial', 'paid') DEFAULT 'unpaid',
            user_id INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (customer_id) REFERENCES customers(id),
            FOREIGN KEY (user_id) REFERENCES users(id)
        )",
        
        // جدول الإعدادات
        "CREATE TABLE IF NOT EXISTS settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(100) UNIQUE NOT NULL,
            setting_value TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )",
        
        // جدول الإشعارات
        "CREATE TABLE IF NOT EXISTS notifications (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT,
            type VARCHAR(50) NOT NULL,
            title VARCHAR(200) NOT NULL,
            message TEXT NOT NULL,
            is_read BOOLEAN DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id)
        )",
        
        // جدول سجل الأنشطة
        "CREATE TABLE IF NOT EXISTS activity_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT,
            action VARCHAR(100) NOT NULL,
            details TEXT,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id)
        )",

        // جدول إرجاع المنتجات
        "CREATE TABLE IF NOT EXISTS product_returns (
            id INT AUTO_INCREMENT PRIMARY KEY,
            sale_id INT NOT NULL,
            product_id INT NOT NULL,
            quantity INT NOT NULL,
            reason TEXT,
            return_amount DECIMAL(10,2) NOT NULL,
            user_id INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (sale_id) REFERENCES sales(id),
            FOREIGN KEY (product_id) REFERENCES products(id),
            FOREIGN KEY (user_id) REFERENCES users(id)
        )",

        // جدول مدفوعات العملاء
        "CREATE TABLE IF NOT EXISTS customer_payments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            customer_id INT NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            payment_type ENUM('payment', 'refund') DEFAULT 'payment',
            reference_type ENUM('sale', 'maintenance', 'manual') DEFAULT 'manual',
            reference_id INT NULL,
            notes TEXT,
            user_id INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (customer_id) REFERENCES customers(id),
            FOREIGN KEY (user_id) REFERENCES users(id)
        )",

        // جدول حركة المخزون
        "CREATE TABLE IF NOT EXISTS stock_movements (
            id INT AUTO_INCREMENT PRIMARY KEY,
            product_id INT NOT NULL,
            movement_type ENUM('in', 'out', 'adjustment') NOT NULL,
            quantity INT NOT NULL,
            reference_type ENUM('sale', 'return', 'adjustment', 'initial') NOT NULL,
            reference_id INT NULL,
            notes TEXT,
            user_id INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (product_id) REFERENCES products(id),
            FOREIGN KEY (user_id) REFERENCES users(id)
        )"
    ];
    
    foreach ($tables as $table) {
        $pdo->exec($table);
    }
    
    // إدراج البيانات الأساسية
    insertDefaultData($pdo);
}

// دالة إدراج البيانات الأساسية
function insertDefaultData($pdo) {
    // إدراج الفئات الأساسية
    $categories = [
        ['اكسسوارات الموبايل', 'جميع اكسسوارات الهواتف المحمولة'],
        ['الهواتف المحمولة', 'جميع أنواع الهواتف المحمولة'],
        ['أجهزة الكمبيوتر', 'أجهزة الكمبيوتر واللابتوب'],
        ['قطع غيار الموبايل', 'قطع غيار للهواتف المحمولة'],
        ['قطع غيار الكمبيوتر', 'قطع غيار لأجهزة الكمبيوتر'],
        ['أدوات الصيانة', 'أدوات ومعدات الصيانة']
    ];

    $stmt = $pdo->prepare("INSERT INTO categories (name, description) VALUES (?, ?)");
    foreach ($categories as $category) {
        $stmt->execute($category);
    }

    // إدراج الإعدادات الأساسية
    $settings = [
        ['shop_name', 'مركز الإلكترونيات والصيانة'],
        ['shop_phone', ''],
        ['shop_email', ''],
        ['shop_address', ''],
        ['shop_whatsapp', ''],
        ['shop_facebook', ''],
        ['shop_instagram', ''],
        ['shop_telegram', ''],
        ['currency', 'دينار عراقي'],
        ['currency_symbol', 'د.ع'],
        ['tax_rate', '0'],
        ['low_stock_alert', '5'],
        ['maintenance_warranty_days', '30'],
        ['invoice_footer_text', 'شكراً لتعاملكم معنا'],
        ['barcode_width', '4.5'],
        ['barcode_height', '2.5']
    ];

    $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?)");
    foreach ($settings as $setting) {
        $stmt->execute($setting);
    }
}

// دالة إنشاء ملف الإعدادات
function createConfigFile($db_config) {
    $config_content = "<?php
// إعدادات قاعدة البيانات
define('DB_HOST', '{$db_config['host']}');
define('DB_NAME', '{$db_config['name']}');
define('DB_USER', '{$db_config['user']}');
define('DB_PASS', '{$db_config['pass']}');

// إعدادات النظام
define('SITE_URL', 'http://' . \$_SERVER['HTTP_HOST'] . dirname(\$_SERVER['SCRIPT_NAME']));
define('UPLOAD_PATH', __DIR__ . '/../uploads/');
define('BACKUP_PATH', __DIR__ . '/../backups/');

// إعدادات الأمان
define('SESSION_TIMEOUT', 3600); // ساعة واحدة
define('MAX_LOGIN_ATTEMPTS', 5);

// تشفير
define('ENCRYPTION_KEY', '" . bin2hex(random_bytes(32)) . "');
?>";
    
    file_put_contents('config/config.php', $config_content);
}

// دالة إنشاء المجلدات
function createDirectories() {
    $directories = [
        'uploads',
        'uploads/products',
        'uploads/customers',
        'uploads/maintenance',
        'backups',
        'logs'
    ];
    
    foreach ($directories as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت نظام إدارة المبيعات</title>
    <link rel="stylesheet" href="assets/css/main.css">
    <style>
        .install-container {
            max-width: 600px;
            margin: 50px auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .install-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .install-body {
            padding: 40px;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }
        
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e0e0e0;
            color: #666;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: bold;
        }
        
        .step.active {
            background: #667eea;
            color: white;
        }
        
        .step.completed {
            background: #4CAF50;
            color: white;
        }
        
        .success-message {
            text-align: center;
            padding: 40px;
        }
        
        .success-icon {
            font-size: 4rem;
            color: #4CAF50;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="install-header">
            <h1>تثبيت نظام إدارة المبيعات</h1>
            <p>مرحباً بك في معالج تثبيت النظام</p>
        </div>
        
        <div class="install-body">
            <?php if ($step < 4): ?>
            <div class="step-indicator">
                <div class="step <?php echo $step >= 1 ? 'active' : ''; ?>">1</div>
                <div class="step <?php echo $step >= 2 ? 'active' : ''; ?>">2</div>
                <div class="step <?php echo $step >= 3 ? 'active' : ''; ?>">3</div>
            </div>
            <?php endif; ?>
            
            <?php if ($error): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="success-message">
                    <div class="success-icon">✓</div>
                    <h2>تم التثبيت بنجاح!</h2>
                    <p><?php echo $success; ?></p>
                    <a href="login.php" class="btn btn-primary">الانتقال إلى تسجيل الدخول</a>
                </div>
            <?php elseif ($step == 1): ?>
                <h2>الخطوة 1: إعدادات قاعدة البيانات</h2>
                <p>يرجى إدخال بيانات الاتصال بقاعدة البيانات</p>
                
                <form method="POST">
                    <div class="form-group">
                        <label class="form-label">خادم قاعدة البيانات</label>
                        <input type="text" name="db_host" class="form-control" value="localhost" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">اسم قاعدة البيانات</label>
                        <input type="text" name="db_name" class="form-control" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">اسم المستخدم</label>
                        <input type="text" name="db_user" class="form-control" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">كلمة المرور</label>
                        <input type="password" name="db_pass" class="form-control">
                    </div>
                    
                    <button type="submit" class="btn btn-primary">التالي</button>
                </form>
                
            <?php elseif ($step == 2): ?>
                <h2>الخطوة 2: إنشاء حساب المدير</h2>
                <p>يرجى إنشاء حساب المدير الرئيسي للنظام</p>
                
                <form method="POST">
                    <div class="form-group">
                        <label class="form-label">اسم المستخدم</label>
                        <input type="text" name="admin_username" class="form-control" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">البريد الإلكتروني</label>
                        <input type="email" name="admin_email" class="form-control" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">كلمة المرور</label>
                        <input type="password" name="admin_password" class="form-control" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">تأكيد كلمة المرور</label>
                        <input type="password" name="confirm_password" class="form-control" required>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">التالي</button>
                </form>
                
            <?php elseif ($step == 3): ?>
                <h2>الخطوة 3: إنهاء التثبيت</h2>
                <p>سيتم الآن إنشاء قاعدة البيانات وتهيئة النظام</p>
                
                <form method="POST">
                    <div class="alert alert-info">
                        <strong>تنبيه:</strong> سيتم إنشاء جميع الجداول والبيانات الأساسية المطلوبة للنظام.
                    </div>
                    
                    <button type="submit" class="btn btn-success">إنهاء التثبيت</button>
                </form>
            <?php endif; ?>
        </div>
    </div>
    
    <script src="assets/js/main.js"></script>
</body>
</html>
