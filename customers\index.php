<?php
session_start();

require_once '../config/config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول
redirectToLogin();

$error = '';
$success = '';

// معالجة إضافة عميل جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'add_customer') {
    try {
        $name = sanitize($_POST['name']);
        $phone = sanitize($_POST['phone']);
        $email = sanitize($_POST['email']);
        $address = sanitize($_POST['address']);
        $notes = sanitize($_POST['notes']);
        
        if (empty($name)) {
            throw new Exception('اسم العميل مطلوب');
        }
        
        if (!empty($phone) && !validatePhone($phone)) {
            throw new Exception('رقم الهاتف غير صحيح');
        }
        
        if (!empty($email) && !validateEmail($email)) {
            throw new Exception('البريد الإلكتروني غير صحيح');
        }
        
        $db = new Database();
        
        // التحقق من عدم تكرار الهاتف أو البريد
        if (!empty($phone)) {
            $existing = $db->fetch("SELECT id FROM customers WHERE phone = ? AND is_active = 1", [$phone]);
            if ($existing) {
                throw new Exception('رقم الهاتف مسجل مسبقاً');
            }
        }
        
        if (!empty($email)) {
            $existing = $db->fetch("SELECT id FROM customers WHERE email = ? AND is_active = 1", [$email]);
            if ($existing) {
                throw new Exception('البريد الإلكتروني مسجل مسبقاً');
            }
        }
        
        $customer_data = [
            'name' => $name,
            'phone' => $phone,
            'email' => $email,
            'address' => $address,
            'notes' => $notes,
            'balance' => 0.00,
            'is_active' => 1
        ];
        
        $customer_id = $db->insert('customers', $customer_data);
        
        if ($customer_id) {
            logActivity('إضافة عميل', "تم إضافة العميل: $name");
            $success = 'تم إضافة العميل بنجاح';
        } else {
            throw new Exception('فشل في إضافة العميل');
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// معالجة حذف عميل
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    try {
        $customer_id = (int)$_GET['delete'];
        $db = new Database();
        
        // التحقق من وجود مبيعات أو صيانة للعميل
        $sales_count = $db->fetch("SELECT COUNT(*) as count FROM sales WHERE customer_id = ?", [$customer_id])['count'];
        $maintenance_count = $db->fetch("SELECT COUNT(*) as count FROM maintenance_requests WHERE customer_id = ?", [$customer_id])['count'];
        
        if ($sales_count > 0 || $maintenance_count > 0) {
            // إلغاء تفعيل بدلاً من الحذف
            $db->update('customers', ['is_active' => 0], 'id = ?', [$customer_id]);
            $success = 'تم إلغاء تفعيل العميل بنجاح';
        } else {
            // حذف نهائي
            $db->delete('customers', 'id = ?', [$customer_id]);
            $success = 'تم حذف العميل بنجاح';
        }
        
        logActivity('حذف عميل', "تم حذف العميل رقم: $customer_id");
        
    } catch (Exception $e) {
        $error = 'فشل في حذف العميل: ' . $e->getMessage();
    }
}

// الحصول على قائمة العملاء
try {
    $db = new Database();
    
    // البحث
    $search = isset($_GET['search']) ? sanitize($_GET['search']) : '';
    $where_clause = "WHERE is_active = 1";
    $params = [];
    
    if (!empty($search)) {
        $where_clause .= " AND (name LIKE ? OR phone LIKE ? OR email LIKE ?)";
        $search_param = "%$search%";
        $params = [$search_param, $search_param, $search_param];
    }
    
    // ترتيب وترقيم الصفحات
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $per_page = 20;
    $offset = ($page - 1) * $per_page;
    
    // العدد الإجمالي
    $total_customers = $db->fetch("SELECT COUNT(*) as count FROM customers $where_clause", $params)['count'];
    $total_pages = ceil($total_customers / $per_page);
    
    // العملاء
    $customers = $db->fetchAll("
        SELECT c.*, 
               (SELECT COUNT(*) FROM sales WHERE customer_id = c.id) as sales_count,
               (SELECT COUNT(*) FROM maintenance_requests WHERE customer_id = c.id) as maintenance_count,
               (SELECT MAX(created_at) FROM sales WHERE customer_id = c.id) as last_sale
        FROM customers c 
        $where_clause 
        ORDER BY c.created_at DESC 
        LIMIT $per_page OFFSET $offset
    ", $params);
    
    // إحصائيات العملاء
    $stats = [
        'total' => $db->fetch("SELECT COUNT(*) as count FROM customers WHERE is_active = 1")['count'],
        'with_debt' => $db->fetch("SELECT COUNT(*) as count FROM customers WHERE balance > 0 AND is_active = 1")['count'],
        'with_credit' => $db->fetch("SELECT COUNT(*) as count FROM customers WHERE balance < 0 AND is_active = 1")['count'],
        'total_debt' => $db->fetch("SELECT COALESCE(SUM(balance), 0) as total FROM customers WHERE balance > 0 AND is_active = 1")['total']
    ];
    
} catch (Exception $e) {
    $error = 'خطأ في تحميل البيانات: ' . $e->getMessage();
    $customers = [];
    $stats = ['total' => 0, 'with_debt' => 0, 'with_credit' => 0, 'total_debt' => 0];
    $total_pages = 1;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العملاء - نظام إدارة المبيعات</title>
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="customers.css">
</head>
<body>
    <?php include '../includes/header.php'; ?>
    
    <div class="container">
        <div class="page-header">
            <h1>👥 إدارة العملاء</h1>
            <p>إضافة وإدارة بيانات العملاء</p>
        </div>
        
        <?php if ($error): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <?php if ($success): ?>
            <div class="alert alert-success"><?php echo $success; ?></div>
        <?php endif; ?>
        
        <!-- إحصائيات العملاء -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">👥</div>
                <div class="stat-info">
                    <div class="stat-number"><?php echo $stats['total']; ?></div>
                    <div class="stat-label">إجمالي العملاء</div>
                </div>
            </div>
            
            <div class="stat-card debt">
                <div class="stat-icon">💰</div>
                <div class="stat-info">
                    <div class="stat-number"><?php echo $stats['with_debt']; ?></div>
                    <div class="stat-label">عملاء لديهم ديون</div>
                </div>
            </div>
            
            <div class="stat-card credit">
                <div class="stat-icon">💳</div>
                <div class="stat-info">
                    <div class="stat-number"><?php echo $stats['with_credit']; ?></div>
                    <div class="stat-label">عملاء لديهم رصيد</div>
                </div>
            </div>
            
            <div class="stat-card total-debt">
                <div class="stat-icon">📊</div>
                <div class="stat-info">
                    <div class="stat-number"><?php echo formatMoney($stats['total_debt']); ?></div>
                    <div class="stat-label">إجمالي الديون</div>
                </div>
            </div>
        </div>
        
        <!-- أدوات البحث والإضافة -->
        <div class="card">
            <div class="card-header">
                <h3>البحث والإضافة</h3>
            </div>
            <div class="tools-section">
                <div class="search-section">
                    <form method="GET" class="search-form">
                        <div class="form-row">
                            <div class="form-col">
                                <input type="text" name="search" class="form-control" 
                                       placeholder="البحث بالاسم أو الهاتف أو البريد الإلكتروني..."
                                       value="<?php echo htmlspecialchars($search); ?>">
                            </div>
                            <div class="form-col-auto">
                                <button type="submit" class="btn btn-primary">🔍 بحث</button>
                                <?php if (!empty($search)): ?>
                                    <a href="index.php" class="btn btn-secondary">مسح البحث</a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </form>
                </div>
                
                <div class="add-section">
                    <button type="button" class="btn btn-success" onclick="showAddCustomerModal()">
                        ➕ إضافة عميل جديد
                    </button>
                    <a href="payments.php" class="btn btn-info">
                        💰 إدارة المدفوعات
                    </a>
                </div>
            </div>
        </div>
        
        <!-- قائمة العملاء -->
        <div class="card">
            <div class="card-header">
                <h3>قائمة العملاء (<?php echo $total_customers; ?> عميل)</h3>
            </div>
            
            <?php if (empty($customers)): ?>
                <div class="empty-state">
                    <div class="empty-icon">👥</div>
                    <h3>لا توجد عملاء</h3>
                    <p>ابدأ بإضافة عميل جديد</p>
                    <button type="button" class="btn btn-primary" onclick="showAddCustomerModal()">
                        إضافة عميل جديد
                    </button>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>اسم العميل</th>
                                <th>رقم الهاتف</th>
                                <th>البريد الإلكتروني</th>
                                <th>الرصيد</th>
                                <th>المبيعات</th>
                                <th>الصيانة</th>
                                <th>آخر عملية</th>
                                <th>إجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($customers as $index => $customer): ?>
                                <tr>
                                    <td><?php echo $offset + $index + 1; ?></td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($customer['name']); ?></strong>
                                        <?php if (!empty($customer['notes'])): ?>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($customer['notes']); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($customer['phone']); ?></td>
                                    <td><?php echo htmlspecialchars($customer['email']); ?></td>
                                    <td>
                                        <?php
                                        $balance_class = '';
                                        if ($customer['balance'] > 0) {
                                            $balance_class = 'text-danger';
                                        } elseif ($customer['balance'] < 0) {
                                            $balance_class = 'text-success';
                                        }
                                        ?>
                                        <span class="<?php echo $balance_class; ?>">
                                            <?php echo formatMoney($customer['balance']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-info"><?php echo $customer['sales_count']; ?></span>
                                    </td>
                                    <td>
                                        <span class="badge badge-warning"><?php echo $customer['maintenance_count']; ?></span>
                                    </td>
                                    <td>
                                        <?php if ($customer['last_sale']): ?>
                                            <?php echo formatDate($customer['last_sale'], 'd/m/Y'); ?>
                                        <?php else: ?>
                                            <span class="text-muted">لا توجد</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="view.php?id=<?php echo $customer['id']; ?>" 
                                               class="btn btn-sm btn-info" title="عرض التفاصيل">
                                                👁️
                                            </a>
                                            <a href="edit.php?id=<?php echo $customer['id']; ?>" 
                                               class="btn btn-sm btn-warning" title="تعديل">
                                                ✏️
                                            </a>
                                            <?php if ($customer['balance'] != 0): ?>
                                                <a href="payments.php?customer_id=<?php echo $customer['id']; ?>" 
                                                   class="btn btn-sm btn-success" title="تسديد">
                                                    💰
                                                </a>
                                            <?php endif; ?>
                                            <button type="button" class="btn btn-sm btn-danger" 
                                                    onclick="deleteCustomer(<?php echo $customer['id']; ?>, '<?php echo htmlspecialchars($customer['name']); ?>')"
                                                    title="حذف">
                                                🗑️
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- ترقيم الصفحات -->
                <?php if ($total_pages > 1): ?>
                    <div class="pagination">
                        <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                            <a href="?page=<?php echo $i; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>" 
                               class="page-link <?php echo $i == $page ? 'active' : ''; ?>">
                                <?php echo $i; ?>
                            </a>
                        <?php endfor; ?>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- نافذة إضافة عميل -->
    <div id="addCustomerModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>إضافة عميل جديد</h3>
                <button class="modal-close" onclick="closeModal('addCustomerModal')">&times;</button>
            </div>
            <form method="POST" id="addCustomerForm">
                <input type="hidden" name="action" value="add_customer">
                <div class="modal-body">
                    <div class="form-row">
                        <div class="form-col">
                            <label class="form-label">اسم العميل *</label>
                            <input type="text" name="name" class="form-control" required>
                        </div>
                        <div class="form-col">
                            <label class="form-label">رقم الهاتف</label>
                            <input type="tel" name="phone" class="form-control">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">البريد الإلكتروني</label>
                        <input type="email" name="email" class="form-control">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">العنوان</label>
                        <textarea name="address" class="form-control" rows="3"></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">ملاحظات</label>
                        <textarea name="notes" class="form-control" rows="2"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-success">💾 حفظ العميل</button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('addCustomerModal')">إلغاء</button>
                </div>
            </form>
        </div>
    </div>
    
    <?php include '../includes/footer.php'; ?>
    
    <script src="../assets/js/main.js"></script>
    <script src="customers.js"></script>
</body>
</html>
