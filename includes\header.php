<?php
// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header('Location: ../login.php');
    exit();
}

// الحصول على الإشعارات غير المقروءة
try {
    $db = new Database();
    $conn = $db->getConnection();
    
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM notifications WHERE user_id = ? AND is_read = 0");
    $stmt->execute([$_SESSION['user_id']]);
    $unreadNotifications = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    // الحصول على آخر 5 إشعارات
    $stmt = $conn->prepare("SELECT * FROM notifications WHERE user_id = ? ORDER BY created_at DESC LIMIT 5");
    $stmt->execute([$_SESSION['user_id']]);
    $recentNotifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $unreadNotifications = 0;
    $recentNotifications = [];
}

// تحديد الصفحة الحالية
$currentPage = basename($_SERVER['PHP_SELF'], '.php');
$currentDir = basename(dirname($_SERVER['PHP_SELF']));
?>
<header class="header">
    <div class="header-content">
        <div class="header-left">
            <div class="logo">
                <a href="../index.php" style="color: white; text-decoration: none;">
                    🏪 نظام إدارة المبيعات
                </a>
            </div>
        </div>
        
        <nav class="main-nav">
            <ul class="nav-menu">
                <li class="nav-item <?php echo ($currentDir == '' && $currentPage == 'index') ? 'active' : ''; ?>">
                    <a href="../index.php" class="nav-link">
                        <span class="nav-icon">🏠</span>
                        الرئيسية
                    </a>
                </li>
                
                <li class="nav-item <?php echo $currentDir == 'sales' ? 'active' : ''; ?>">
                    <a href="../sales/" class="nav-link">
                        <span class="nav-icon">🛒</span>
                        المبيعات
                    </a>
                </li>
                
                <li class="nav-item <?php echo $currentDir == 'customers' ? 'active' : ''; ?>">
                    <a href="../customers/" class="nav-link">
                        <span class="nav-icon">👥</span>
                        العملاء
                    </a>
                </li>
                
                <li class="nav-item <?php echo $currentDir == 'products' ? 'active' : ''; ?>">
                    <a href="../products/" class="nav-link">
                        <span class="nav-icon">📦</span>
                        المنتجات
                    </a>
                </li>
                
                <li class="nav-item <?php echo $currentDir == 'maintenance' ? 'active' : ''; ?>">
                    <a href="../maintenance/" class="nav-link">
                        <span class="nav-icon">🔧</span>
                        الصيانة
                    </a>
                </li>
                
                <li class="nav-item <?php echo $currentDir == 'invoices' ? 'active' : ''; ?>">
                    <a href="../invoices/" class="nav-link">
                        <span class="nav-icon">📄</span>
                        الفواتير
                    </a>
                </li>
                
                <li class="nav-item <?php echo $currentDir == 'reports' ? 'active' : ''; ?>">
                    <a href="../reports/" class="nav-link">
                        <span class="nav-icon">📊</span>
                        التقارير
                    </a>
                </li>
                
                <?php if ($_SESSION['role'] == 'admin'): ?>
                <li class="nav-item dropdown">
                    <a href="#" class="nav-link dropdown-toggle">
                        <span class="nav-icon">⚙️</span>
                        الإدارة
                        <span class="dropdown-arrow">▼</span>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a href="../settings/" class="dropdown-link">الإعدادات</a></li>
                        <li><a href="../logs/" class="dropdown-link">السجلات</a></li>
                        <li><a href="../notifications/" class="dropdown-link">التنبيهات</a></li>
                    </ul>
                </li>
                <?php endif; ?>
            </ul>
        </nav>
        
        <div class="header-right">
            <!-- الإشعارات -->
            <div class="notifications-dropdown">
                <button class="notifications-btn" onclick="toggleNotifications()">
                    <span class="notification-icon">🔔</span>
                    <?php if ($unreadNotifications > 0): ?>
                        <span class="notification-badge"><?php echo $unreadNotifications; ?></span>
                    <?php endif; ?>
                </button>
                
                <div class="notifications-menu" id="notificationsMenu">
                    <div class="notifications-header">
                        <h4>الإشعارات</h4>
                        <?php if ($unreadNotifications > 0): ?>
                            <button class="mark-all-read" onclick="markAllAsRead()">تعيين الكل كمقروء</button>
                        <?php endif; ?>
                    </div>
                    
                    <div class="notifications-list">
                        <?php if (empty($recentNotifications)): ?>
                            <div class="notification-item">
                                <p>لا توجد إشعارات</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($recentNotifications as $notification): ?>
                                <div class="notification-item <?php echo $notification['is_read'] ? '' : 'unread'; ?>">
                                    <div class="notification-content">
                                        <h5><?php echo htmlspecialchars($notification['title']); ?></h5>
                                        <p><?php echo htmlspecialchars($notification['message']); ?></p>
                                        <small><?php echo formatDate($notification['created_at'], 'd/m/Y H:i'); ?></small>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                    
                    <div class="notifications-footer">
                        <a href="../notifications/" class="view-all-notifications">عرض جميع الإشعارات</a>
                    </div>
                </div>
            </div>
            
            <!-- معلومات المستخدم -->
            <div class="user-dropdown">
                <button class="user-btn" onclick="toggleUserMenu()">
                    <span class="user-avatar">👤</span>
                    <span class="user-name"><?php echo htmlspecialchars($_SESSION['username']); ?></span>
                    <span class="dropdown-arrow">▼</span>
                </button>
                
                <div class="user-menu" id="userMenu">
                    <div class="user-info">
                        <h4><?php echo htmlspecialchars($_SESSION['username']); ?></h4>
                        <p><?php echo htmlspecialchars($_SESSION['email']); ?></p>
                        <small>الدور: <?php echo $_SESSION['role'] == 'admin' ? 'مدير' : 'مستخدم'; ?></small>
                    </div>
                    
                    <div class="user-menu-items">
                        <a href="../profile.php" class="user-menu-item">
                            <span class="menu-icon">👤</span>
                            الملف الشخصي
                        </a>
                        
                        <a href="../settings/" class="user-menu-item">
                            <span class="menu-icon">⚙️</span>
                            الإعدادات
                        </a>
                        
                        <div class="menu-divider"></div>
                        
                        <a href="../logout.php" class="user-menu-item logout" onclick="return confirm('هل أنت متأكد من تسجيل الخروج؟')">
                            <span class="menu-icon">🚪</span>
                            تسجيل الخروج
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>

<style>
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    height: 70px;
}

.header-left .logo {
    font-size: 1.5rem;
    font-weight: bold;
}

.main-nav .nav-menu {
    display: flex;
    list-style: none;
    gap: 0;
    margin: 0;
    padding: 0;
}

.nav-item {
    position: relative;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 8px;
    color: white;
    text-decoration: none;
    padding: 20px 15px;
    transition: background-color 0.3s;
    height: 70px;
    box-sizing: border-box;
}

.nav-link:hover,
.nav-item.active .nav-link {
    background-color: rgba(255,255,255,0.2);
    text-decoration: none;
    color: white;
}

.nav-icon {
    font-size: 1.2rem;
}

.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    min-width: 150px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s;
    z-index: 1001;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-link {
    display: block;
    padding: 10px 15px;
    color: #333;
    text-decoration: none;
    transition: background-color 0.3s;
}

.dropdown-link:hover {
    background-color: #f8f9fa;
    text-decoration: none;
    color: #333;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.notifications-dropdown,
.user-dropdown {
    position: relative;
}

.notifications-btn,
.user-btn {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 10px;
    border-radius: 5px;
    transition: background-color 0.3s;
    display: flex;
    align-items: center;
    gap: 8px;
}

.notifications-btn:hover,
.user-btn:hover {
    background-color: rgba(255,255,255,0.2);
}

.notification-badge {
    background: #f44336;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 0.8rem;
    position: absolute;
    top: 5px;
    left: 5px;
}

.notifications-menu,
.user-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    min-width: 300px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s;
    z-index: 1001;
    color: #333;
}

.notifications-menu.show,
.user-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.notifications-header {
    padding: 15px 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.notifications-header h4 {
    margin: 0;
    font-size: 1.1rem;
}

.mark-all-read {
    background: none;
    border: none;
    color: #667eea;
    cursor: pointer;
    font-size: 0.9rem;
}

.notifications-list {
    max-height: 300px;
    overflow-y: auto;
}

.notification-item {
    padding: 15px 20px;
    border-bottom: 1px solid #f8f9fa;
    transition: background-color 0.3s;
}

.notification-item:hover {
    background-color: #f8f9fa;
}

.notification-item.unread {
    background-color: #e3f2fd;
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-content h5 {
    margin: 0 0 5px 0;
    font-size: 0.9rem;
    color: #333;
}

.notification-content p {
    margin: 0 0 5px 0;
    font-size: 0.8rem;
    color: #666;
}

.notification-content small {
    color: #999;
    font-size: 0.7rem;
}

.notifications-footer {
    padding: 15px 20px;
    border-top: 1px solid #f0f0f0;
    text-align: center;
}

.view-all-notifications {
    color: #667eea;
    text-decoration: none;
    font-size: 0.9rem;
}

.user-info {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    text-align: center;
}

.user-info h4 {
    margin: 0 0 5px 0;
    color: #333;
}

.user-info p {
    margin: 0 0 5px 0;
    color: #666;
    font-size: 0.9rem;
}

.user-info small {
    color: #999;
    font-size: 0.8rem;
}

.user-menu-items {
    padding: 10px 0;
}

.user-menu-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 20px;
    color: #333;
    text-decoration: none;
    transition: background-color 0.3s;
}

.user-menu-item:hover {
    background-color: #f8f9fa;
    text-decoration: none;
    color: #333;
}

.user-menu-item.logout {
    color: #f44336;
}

.user-menu-item.logout:hover {
    background-color: #ffebee;
    color: #f44336;
}

.menu-divider {
    height: 1px;
    background: #f0f0f0;
    margin: 10px 0;
}

.menu-icon {
    font-size: 1rem;
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .header-content {
        padding: 0 10px;
        height: auto;
        flex-direction: column;
        gap: 10px;
        padding: 10px;
    }
    
    .main-nav .nav-menu {
        flex-wrap: wrap;
        justify-content: center;
        gap: 5px;
    }
    
    .nav-link {
        padding: 10px;
        height: auto;
        font-size: 0.9rem;
    }
    
    .nav-icon {
        font-size: 1rem;
    }
    
    .header-right {
        gap: 10px;
    }
    
    .notifications-menu,
    .user-menu {
        min-width: 250px;
        right: -50px;
    }
}

@media (max-width: 480px) {
    .nav-menu {
        flex-direction: column;
        width: 100%;
    }
    
    .nav-link {
        justify-content: center;
        padding: 8px;
    }
    
    .notifications-menu,
    .user-menu {
        min-width: 200px;
        right: -100px;
    }
}
</style>

<script>
// تبديل عرض الإشعارات
function toggleNotifications() {
    const menu = document.getElementById('notificationsMenu');
    const userMenu = document.getElementById('userMenu');
    
    // إخفاء قائمة المستخدم
    userMenu.classList.remove('show');
    
    // تبديل قائمة الإشعارات
    menu.classList.toggle('show');
}

// تبديل عرض قائمة المستخدم
function toggleUserMenu() {
    const menu = document.getElementById('userMenu');
    const notificationsMenu = document.getElementById('notificationsMenu');
    
    // إخفاء قائمة الإشعارات
    notificationsMenu.classList.remove('show');
    
    // تبديل قائمة المستخدم
    menu.classList.toggle('show');
}

// تعيين جميع الإشعارات كمقروءة
function markAllAsRead() {
    fetch('../api/mark_notifications_read.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        }
    })
    .catch(error => {
        console.error('خطأ في تعيين الإشعارات كمقروءة:', error);
    });
}

// إخفاء القوائم عند النقر خارجها
document.addEventListener('click', function(e) {
    const notificationsDropdown = document.querySelector('.notifications-dropdown');
    const userDropdown = document.querySelector('.user-dropdown');
    const notificationsMenu = document.getElementById('notificationsMenu');
    const userMenu = document.getElementById('userMenu');
    
    if (!notificationsDropdown.contains(e.target)) {
        notificationsMenu.classList.remove('show');
    }
    
    if (!userDropdown.contains(e.target)) {
        userMenu.classList.remove('show');
    }
});
</script>
