/* ملف CSS خاص بطباعة الباركود */

/* إعدادات الطباعة */
.settings-section {
    padding: 20px;
}

.preview-actions {
    margin-top: 20px;
    text-align: center;
}

.preview-actions .btn {
    margin: 0 5px;
}

/* قسم البحث */
.search-section {
    padding: 20px;
}

.search-form .form-row {
    margin-bottom: 15px;
}

.bulk-actions {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #f0f0f0;
}

.selected-count {
    font-weight: 600;
    color: #667eea;
}

/* شبكة المنتجات */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    padding: 20px;
}

.product-card {
    background: white;
    border: 2px solid #f0f0f0;
    border-radius: 10px;
    padding: 15px;
    transition: all 0.3s;
    position: relative;
}

.product-card:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
}

.product-card.selected {
    border-color: #28a745;
    background: rgba(40, 167, 69, 0.05);
}

.product-select {
    position: absolute;
    top: 10px;
    left: 10px;
}

.product-checkbox {
    width: 20px;
    height: 20px;
    cursor: pointer;
}

.product-info {
    margin: 10px 0 15px 0;
}

.product-info h4 {
    margin: 0 0 8px 0;
    color: #333;
    font-size: 1rem;
    line-height: 1.3;
}

.product-category {
    color: #666;
    font-size: 0.85rem;
    margin: 0 0 8px 0;
}

.product-barcode {
    margin: 8px 0;
}

.product-barcode code {
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    color: #333;
}

.product-price {
    color: #28a745;
    font-weight: bold;
    font-size: 1.1rem;
    margin: 8px 0;
}

.product-quantity {
    margin: 15px 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.product-quantity label {
    font-size: 0.9rem;
    color: #666;
    min-width: 80px;
}

.quantity-input {
    width: 80px;
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-align: center;
}

.product-actions {
    display: flex;
    gap: 8px;
    justify-content: center;
}

/* معاينة الطباعة */
.preview-container {
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    margin: 20px;
}

#preview-content {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow-x: auto;
}

/* تخطيط الباركود */
.barcode-page {
    width: 21cm; /* A4 width */
    min-height: 29.7cm; /* A4 height */
    margin: 0 auto 20px auto;
    padding: 1cm;
    background: white;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    page-break-after: always;
}

.barcode-page:last-child {
    page-break-after: avoid;
}

.barcode-row {
    display: flex;
    margin-bottom: 2mm;
}

.barcode-label {
    border: 1px solid #ddd;
    margin-left: 2mm;
    display: flex;
    align-items: center;
    justify-content: center;
    page-break-inside: avoid;
    background: white;
}

.barcode-label:first-child {
    margin-left: 0;
}

.barcode-content {
    text-align: center;
    padding: 1mm;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.product-name {
    font-size: 8px;
    font-weight: bold;
    margin-bottom: 1mm;
    line-height: 1.2;
    overflow: hidden;
    text-overflow: ellipsis;
    max-height: 3 lines;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.barcode-image {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 1mm 0;
}

.barcode-image svg {
    max-width: 100%;
    max-height: 100%;
}

.barcode-text {
    font-size: 6px;
    margin-top: 1mm;
    font-family: 'Courier New', monospace;
    color: #333;
}

.product-price {
    font-size: 7px;
    font-weight: bold;
    margin-top: 1mm;
    color: #28a745;
}

/* خانات الاختيار المخصصة */
.form-check {
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-check-input {
    width: 18px;
    height: 18px;
    cursor: pointer;
}

.form-check-label {
    cursor: pointer;
    font-size: 0.9rem;
    color: #555;
}

/* الحالة الفارغة */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

.empty-state h3 {
    margin: 0 0 10px 0;
    color: #333;
}

.empty-state p {
    margin: 0 0 20px 0;
    color: #666;
}

/* تحسينات الطباعة */
@media print {
    body {
        margin: 0;
        padding: 0;
    }
    
    .container,
    .page-header,
    .card:not(#print-preview),
    .btn,
    .preview-actions {
        display: none !important;
    }
    
    #print-preview {
        display: block !important;
        box-shadow: none;
        border: none;
        margin: 0;
        padding: 0;
    }
    
    .preview-container {
        background: none;
        padding: 0;
        margin: 0;
    }
    
    #preview-content {
        background: none;
        padding: 0;
        box-shadow: none;
        border-radius: 0;
    }
    
    .barcode-page {
        box-shadow: none;
        margin: 0;
        page-break-after: always;
    }
    
    .barcode-page:last-child {
        page-break-after: avoid;
    }
    
    @page {
        size: A4;
        margin: 0;
    }
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .products-grid {
        grid-template-columns: 1fr;
        gap: 15px;
        padding: 15px;
    }
    
    .product-card {
        padding: 12px;
    }
    
    .bulk-actions {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
    
    .preview-actions {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }
    
    .preview-actions .btn {
        margin: 0;
        width: 100%;
    }
    
    .form-row {
        flex-direction: column;
        gap: 10px;
    }
    
    .barcode-page {
        width: 100%;
        min-height: auto;
        padding: 5mm;
        transform: scale(0.8);
        transform-origin: top center;
    }
    
    .preview-container {
        padding: 10px;
        margin: 10px;
    }
}

@media (max-width: 480px) {
    .product-actions {
        flex-direction: column;
        gap: 5px;
    }
    
    .product-actions .btn {
        width: 100%;
        font-size: 0.8rem;
        padding: 6px 8px;
    }
    
    .product-quantity {
        flex-direction: column;
        align-items: stretch;
        gap: 5px;
    }
    
    .product-quantity label {
        min-width: auto;
        text-align: center;
    }
    
    .quantity-input {
        width: 100%;
    }
}

/* تأثيرات إضافية */
.product-card.printing {
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.barcode-label.preview {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: scale(0.9); }
    to { opacity: 1; transform: scale(1); }
}

/* تحسينات إضافية للباركود */
.barcode-label {
    position: relative;
    overflow: hidden;
}

.barcode-label::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 49%, rgba(255,255,255,0.1) 50%, transparent 51%);
    pointer-events: none;
}

/* تحسين عرض الأسعار */
.product-price.large {
    font-size: 9px;
    color: #dc3545;
}

.product-price.medium {
    font-size: 7px;
    color: #28a745;
}

.product-price.small {
    font-size: 6px;
    color: #6c757d;
}

/* تحسين عرض أسماء المنتجات الطويلة */
.product-name.long {
    font-size: 6px;
    line-height: 1.1;
}

.product-name.medium {
    font-size: 7px;
    line-height: 1.2;
}

.product-name.short {
    font-size: 8px;
    line-height: 1.3;
}
