<?php
session_start();

// التحقق من وجود ملف الإعدادات
if (!file_exists('config/config.php')) {
    header('Location: install.php');
    exit();
}

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'includes/functions.php';

// إعادة توجيه المستخدم المسجل دخوله
if (isLoggedIn()) {
    header('Location: index.php');
    exit();
}

$error = '';
$success = '';

// معالجة تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitize($_POST['username']);
    $password = $_POST['password'];
    $remember = isset($_POST['remember']);
    
    if (empty($username) || empty($password)) {
        $error = 'يرجى إدخال اسم المستخدم وكلمة المرور';
    } else {
        try {
            $db = new Database();
            $conn = $db->getConnection();
            
            // البحث عن المستخدم
            $stmt = $conn->prepare("SELECT id, username, email, password, role, is_active FROM users WHERE (username = ? OR email = ?) AND is_active = 1");
            $stmt->execute([$username, $username]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user && verifyPassword($password, $user['password'])) {
                // تسجيل دخول ناجح
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['email'] = $user['email'];
                $_SESSION['role'] = $user['role'];
                $_SESSION['login_time'] = time();
                
                // تحديث وقت آخر دخول
                $stmt = $conn->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
                $stmt->execute([$user['id']]);
                
                // تسجيل النشاط
                logActivity('تسجيل دخول', 'تسجيل دخول ناجح', $user['id']);
                
                // تذكر المستخدم
                if ($remember) {
                    $token = generateToken();
                    setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/'); // 30 يوم
                    
                    // حفظ الرمز في قاعدة البيانات
                    $stmt = $conn->prepare("UPDATE users SET remember_token = ? WHERE id = ?");
                    $stmt->execute([$token, $user['id']]);
                }
                
                // إعادة التوجيه
                $redirect = isset($_GET['redirect']) ? $_GET['redirect'] : 'index.php';
                header('Location: ' . $redirect);
                exit();
                
            } else {
                $error = 'اسم المستخدم أو كلمة المرور غير صحيحة';
                
                // تسجيل محاولة دخول فاشلة
                logActivity('محاولة دخول فاشلة', "اسم المستخدم: $username");
            }
            
        } catch (Exception $e) {
            $error = 'خطأ في النظام، يرجى المحاولة لاحقاً';
            error_log("خطأ في تسجيل الدخول: " . $e->getMessage());
        }
    }
}

// التحقق من رمز التذكر
if (isset($_COOKIE['remember_token']) && !isLoggedIn()) {
    try {
        $db = new Database();
        $conn = $db->getConnection();
        
        $stmt = $conn->prepare("SELECT id, username, email, role FROM users WHERE remember_token = ? AND is_active = 1");
        $stmt->execute([$_COOKIE['remember_token']]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user) {
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['email'] = $user['email'];
            $_SESSION['role'] = $user['role'];
            $_SESSION['login_time'] = time();
            
            header('Location: index.php');
            exit();
        }
    } catch (Exception $e) {
        // تجاهل الخطأ
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة المبيعات</title>
    <link rel="stylesheet" href="assets/css/main.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 100%;
            max-width: 400px;
            margin: 20px;
        }
        
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }
        
        .login-header h1 {
            margin: 0;
            font-size: 1.8rem;
            margin-bottom: 10px;
        }
        
        .login-header p {
            margin: 0;
            opacity: 0.9;
        }
        
        .login-body {
            padding: 40px 30px;
        }
        
        .login-form .form-group {
            margin-bottom: 25px;
        }
        
        .login-form .form-control {
            padding: 15px;
            font-size: 16px;
            border-radius: 8px;
        }
        
        .login-form .btn {
            width: 100%;
            padding: 15px;
            font-size: 16px;
            border-radius: 8px;
            margin-top: 10px;
        }
        
        .remember-me {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .remember-me input[type="checkbox"] {
            width: auto;
        }
        
        .login-footer {
            text-align: center;
            padding: 20px 30px;
            background: #f8f9fa;
            border-top: 1px solid #e0e0e0;
        }
        
        .system-info {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            text-align: center;
        }
        
        .system-info h3 {
            margin: 0 0 10px 0;
            color: white;
        }
        
        .system-info p {
            margin: 0;
            color: rgba(255,255,255,0.8);
            font-size: 14px;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .feature {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            color: white;
        }
        
        .feature-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .feature h4 {
            margin: 0 0 10px 0;
            font-size: 1.1rem;
        }
        
        .feature p {
            margin: 0;
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        @media (max-width: 768px) {
            .login-container {
                margin: 10px;
            }
            
            .login-header {
                padding: 30px 20px;
            }
            
            .login-body {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container fade-in">
        <div class="login-header">
            <h1>نظام إدارة المبيعات</h1>
            <p>مرحباً بك في نظام إدارة المحلات</p>
        </div>
        
        <div class="login-body">
            <?php if ($error): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>
            
            <form method="POST" class="login-form">
                <div class="form-group">
                    <label class="form-label">اسم المستخدم أو البريد الإلكتروني</label>
                    <input type="text" name="username" class="form-control" required 
                           value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>"
                           placeholder="أدخل اسم المستخدم أو البريد الإلكتروني">
                </div>
                
                <div class="form-group">
                    <label class="form-label">كلمة المرور</label>
                    <input type="password" name="password" class="form-control" required 
                           placeholder="أدخل كلمة المرور">
                </div>
                
                <div class="remember-me">
                    <input type="checkbox" name="remember" id="remember">
                    <label for="remember">تذكرني</label>
                </div>
                
                <button type="submit" class="btn btn-primary">تسجيل الدخول</button>
            </form>
        </div>
        
        <div class="login-footer">
            <p>&copy; 2024 نظام إدارة المبيعات. جميع الحقوق محفوظة.</p>
        </div>
    </div>
    
    <!-- معلومات النظام -->
    <div class="system-info fade-in" style="position: fixed; bottom: 20px; left: 20px; max-width: 300px;">
        <h3>مميزات النظام</h3>
        <p>نظام شامل لإدارة المبيعات والصيانة مع واجهة سهلة الاستخدام</p>
    </div>
    
    <!-- المميزات -->
    <div class="features" style="position: fixed; top: 20px; right: 20px; max-width: 600px;">
        <div class="feature fade-in">
            <div class="feature-icon">🛒</div>
            <h4>إدارة المبيعات</h4>
            <p>نظام مبيعات متطور مع البحث والباركود</p>
        </div>
        
        <div class="feature fade-in" style="animation-delay: 0.2s;">
            <div class="feature-icon">🔧</div>
            <h4>إدارة الصيانة</h4>
            <p>متابعة طلبات الصيانة وحالاتها</p>
        </div>
        
        <div class="feature fade-in" style="animation-delay: 0.4s;">
            <div class="feature-icon">📊</div>
            <h4>التقارير</h4>
            <p>تقارير مفصلة للمبيعات والأرباح</p>
        </div>
    </div>
    
    <script src="assets/js/main.js"></script>
    <script>
        // تأثيرات إضافية لصفحة تسجيل الدخول
        document.addEventListener('DOMContentLoaded', function() {
            // تركيز على حقل اسم المستخدم
            const usernameField = document.querySelector('input[name="username"]');
            if (usernameField) {
                usernameField.focus();
            }
            
            // إضافة تأثير للمميزات
            const features = document.querySelectorAll('.feature');
            features.forEach((feature, index) => {
                setTimeout(() => {
                    feature.style.transform = 'translateY(0)';
                    feature.style.opacity = '1';
                }, index * 200);
            });
        });
        
        // التحقق من caps lock
        document.querySelector('input[name="password"]').addEventListener('keyup', function(e) {
            if (e.getModifierState && e.getModifierState('CapsLock')) {
                showNotification('تنبيه: مفتاح Caps Lock مفعل', 'warning');
            }
        });
    </script>
</body>
</html>
