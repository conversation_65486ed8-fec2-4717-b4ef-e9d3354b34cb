// ملف JavaScript الرئيسي للنظام

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeSystem();
});

// تهيئة النظام
function initializeSystem() {
    // إضافة تأثيرات الأنيميشن
    addAnimations();
    
    // تهيئة النماذج
    initializeForms();
    
    // تهيئة الجداول
    initializeTables();
    
    // تهيئة الإشعارات
    initializeNotifications();
    
    // تهيئة البحث
    initializeSearch();
    
    // فحص التنبيهات
    checkNotifications();
}

// إضافة تأثيرات الأنيميشن
function addAnimations() {
    // إضافة تأثير الظهور للبطاقات
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        setTimeout(() => {
            card.classList.add('fade-in');
        }, index * 100);
    });
    
    // إضافة تأثير hover للأزرار
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
}

// تهيئة النماذج
function initializeForms() {
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        // إضافة التحقق من صحة البيانات
        form.addEventListener('submit', function(e) {
            if (!validateForm(this)) {
                e.preventDefault();
                return false;
            }
            
            // إضافة مؤشر التحميل
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<span class="loading"></span> جاري المعالجة...';
                submitBtn.disabled = true;
            }
        });
        
        // إضافة التحقق الفوري للحقول
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                validateField(this);
            });
        });
    });
}

// التحقق من صحة النموذج
function validateForm(form) {
    let isValid = true;
    const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
    
    inputs.forEach(input => {
        if (!validateField(input)) {
            isValid = false;
        }
    });
    
    return isValid;
}

// التحقق من صحة الحقل
function validateField(field) {
    const value = field.value.trim();
    const fieldType = field.type;
    let isValid = true;
    let errorMessage = '';
    
    // إزالة رسائل الخطأ السابقة
    removeFieldError(field);
    
    // التحقق من الحقول المطلوبة
    if (field.hasAttribute('required') && !value) {
        isValid = false;
        errorMessage = 'هذا الحقل مطلوب';
    }
    
    // التحقق من البريد الإلكتروني
    else if (fieldType === 'email' && value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            isValid = false;
            errorMessage = 'البريد الإلكتروني غير صحيح';
        }
    }
    
    // التحقق من رقم الهاتف
    else if (field.name === 'phone' && value) {
        const phoneRegex = /^[0-9+\-\s()]+$/;
        if (!phoneRegex.test(value)) {
            isValid = false;
            errorMessage = 'رقم الهاتف غير صحيح';
        }
    }
    
    // التحقق من الأرقام
    else if (fieldType === 'number' && value) {
        if (isNaN(value) || parseFloat(value) < 0) {
            isValid = false;
            errorMessage = 'يجب أن يكون رقماً صحيحاً';
        }
    }
    
    // عرض رسالة الخطأ
    if (!isValid) {
        showFieldError(field, errorMessage);
    }
    
    return isValid;
}

// عرض رسالة خطأ للحقل
function showFieldError(field, message) {
    field.style.borderColor = '#dc3545';
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'field-error';
    errorDiv.style.color = '#dc3545';
    errorDiv.style.fontSize = '12px';
    errorDiv.style.marginTop = '5px';
    errorDiv.textContent = message;
    
    field.parentNode.appendChild(errorDiv);
}

// إزالة رسالة خطأ الحقل
function removeFieldError(field) {
    field.style.borderColor = '#e0e0e0';
    
    const errorDiv = field.parentNode.querySelector('.field-error');
    if (errorDiv) {
        errorDiv.remove();
    }
}

// تهيئة الجداول
function initializeTables() {
    const tables = document.querySelectorAll('.table');
    
    tables.forEach(table => {
        // إضافة إمكانية الترتيب
        const headers = table.querySelectorAll('th[data-sort]');
        headers.forEach(header => {
            header.style.cursor = 'pointer';
            header.addEventListener('click', function() {
                sortTable(table, this.dataset.sort);
            });
        });
        
        // إضافة تأثير hover للصفوف
        const rows = table.querySelectorAll('tbody tr');
        rows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.backgroundColor = '#f8f9fa';
            });
            
            row.addEventListener('mouseleave', function() {
                this.style.backgroundColor = '';
            });
        });
    });
}

// ترتيب الجدول
function sortTable(table, column) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    
    rows.sort((a, b) => {
        const aValue = a.querySelector(`td[data-${column}]`).textContent.trim();
        const bValue = b.querySelector(`td[data-${column}]`).textContent.trim();
        
        if (!isNaN(aValue) && !isNaN(bValue)) {
            return parseFloat(aValue) - parseFloat(bValue);
        }
        
        return aValue.localeCompare(bValue, 'ar');
    });
    
    rows.forEach(row => tbody.appendChild(row));
}

// تهيئة الإشعارات
function initializeNotifications() {
    // إخفاء الإشعارات تلقائياً بعد 5 ثوان
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            alert.style.opacity = '0';
            setTimeout(() => {
                alert.remove();
            }, 300);
        }, 5000);
    });
}

// عرض إشعار
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} fade-in`;
    notification.textContent = message;
    
    // إضافة زر الإغلاق
    const closeBtn = document.createElement('button');
    closeBtn.innerHTML = '×';
    closeBtn.style.float = 'left';
    closeBtn.style.background = 'none';
    closeBtn.style.border = 'none';
    closeBtn.style.fontSize = '20px';
    closeBtn.style.cursor = 'pointer';
    closeBtn.onclick = () => notification.remove();
    
    notification.appendChild(closeBtn);
    
    // إضافة الإشعار إلى الصفحة
    const container = document.querySelector('.container') || document.body;
    container.insertBefore(notification, container.firstChild);
    
    // إزالة الإشعار تلقائياً
    setTimeout(() => {
        notification.remove();
    }, 5000);
}

// تهيئة البحث
function initializeSearch() {
    const searchInputs = document.querySelectorAll('input[data-search]');
    
    searchInputs.forEach(input => {
        input.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const targetTable = document.querySelector(this.dataset.search);
            
            if (targetTable) {
                filterTable(targetTable, searchTerm);
            }
        });
    });
}

// تصفية الجدول
function filterTable(table, searchTerm) {
    const rows = table.querySelectorAll('tbody tr');
    
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        if (text.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

// فحص التنبيهات
function checkNotifications() {
    fetch('../api/check_notifications.php')
        .then(response => response.json())
        .then(data => {
            if (data.notifications && data.notifications.length > 0) {
                data.notifications.forEach(notification => {
                    showNotification(notification.message, notification.type);
                });
            }
        })
        .catch(error => {
            console.error('خطأ في فحص التنبيهات:', error);
        });
}

// طباعة الفاتورة
function printInvoice(invoiceId) {
    const printWindow = window.open(`../invoices/print.php?id=${invoiceId}`, '_blank');
    printWindow.onload = function() {
        printWindow.print();
    };
}

// تأكيد الحذف
function confirmDelete(message = 'هل أنت متأكد من الحذف؟') {
    return confirm(message);
}

// تحديث الصفحة
function refreshPage() {
    location.reload();
}

// إعادة تعيين النموذج
function resetForm(formId) {
    const form = document.getElementById(formId);
    if (form) {
        form.reset();
        
        // إزالة رسائل الخطأ
        const errors = form.querySelectorAll('.field-error');
        errors.forEach(error => error.remove());
        
        // إعادة تعيين ألوان الحدود
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.style.borderColor = '#e0e0e0';
        });
    }
}

// تحويل التاريخ إلى تنسيق عربي
function formatDateArabic(dateString) {
    const date = new Date(dateString);
    const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    };
    
    return date.toLocaleDateString('ar-SA', options);
}

// تنسيق المبلغ
function formatMoney(amount, currency = 'ريال') {
    return parseFloat(amount).toLocaleString('ar-SA', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }) + ' ' + currency;
}

// إضافة منتج إلى الفاتورة
function addProductToInvoice(productId, productName, price) {
    const invoiceTable = document.getElementById('invoice-items');
    if (!invoiceTable) return;
    
    const row = document.createElement('tr');
    row.innerHTML = `
        <td>${productName}</td>
        <td><input type="number" value="1" min="1" class="form-control quantity-input" onchange="updateRowTotal(this)"></td>
        <td class="price">${price}</td>
        <td class="total">${price}</td>
        <td><button type="button" class="btn btn-danger btn-sm" onclick="removeInvoiceRow(this)">حذف</button></td>
        <input type="hidden" name="products[]" value="${productId}">
    `;
    
    invoiceTable.appendChild(row);
    updateInvoiceTotal();
}

// إزالة صف من الفاتورة
function removeInvoiceRow(button) {
    button.closest('tr').remove();
    updateInvoiceTotal();
}

// تحديث إجمالي الصف
function updateRowTotal(input) {
    const row = input.closest('tr');
    const quantity = parseFloat(input.value) || 0;
    const price = parseFloat(row.querySelector('.price').textContent) || 0;
    const total = quantity * price;
    
    row.querySelector('.total').textContent = total.toFixed(2);
    updateInvoiceTotal();
}

// تحديث إجمالي الفاتورة
function updateInvoiceTotal() {
    const totals = document.querySelectorAll('#invoice-items .total');
    let grandTotal = 0;
    
    totals.forEach(total => {
        grandTotal += parseFloat(total.textContent) || 0;
    });
    
    const totalElement = document.getElementById('invoice-total');
    if (totalElement) {
        totalElement.textContent = formatMoney(grandTotal);
    }
}

// البحث بالباركود
function searchByBarcode() {
    const barcodeInput = document.getElementById('barcode-input');
    if (!barcodeInput) return;
    
    const barcode = barcodeInput.value.trim();
    if (!barcode) return;
    
    fetch('../api/search_product.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ barcode: barcode })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.product) {
            addProductToInvoice(data.product.id, data.product.name, data.product.price);
            barcodeInput.value = '';
        } else {
            showNotification('المنتج غير موجود', 'warning');
        }
    })
    .catch(error => {
        console.error('خطأ في البحث:', error);
        showNotification('خطأ في البحث عن المنتج', 'danger');
    });
}
