<footer class="footer">
    <div class="footer-content">
        <div class="footer-section">
            <h4>نظام إدارة المبيعات</h4>
            <p>نظام شامل لإدارة المحلات والمبيعات والصيانة</p>
            <div class="footer-stats">
                <span>آخر تحديث: <?php echo date('d/m/Y H:i'); ?></span>
            </div>
        </div>
        
        <div class="footer-section">
            <h4>روابط سريعة</h4>
            <ul class="footer-links">
                <li><a href="../index.php">الصفحة الرئيسية</a></li>
                <li><a href="../sales/">المبيعات</a></li>
                <li><a href="../maintenance/">الصيانة</a></li>
                <li><a href="../reports/">التقارير</a></li>
            </ul>
        </div>
        
        <div class="footer-section">
            <h4>الدعم</h4>
            <ul class="footer-links">
                <li><a href="../help.php">المساعدة</a></li>
                <li><a href="../contact.php">اتصل بنا</a></li>
                <li><a href="../docs.php">الوثائق</a></li>
                <li><a href="../backup.php">النسخ الاحتياطي</a></li>
            </ul>
        </div>
        
        <div class="footer-section">
            <h4>معلومات النظام</h4>
            <div class="system-info">
                <p>الإصدار: 1.0.0</p>
                <p>PHP: <?php echo PHP_VERSION; ?></p>
                <p>المستخدم: <?php echo htmlspecialchars($_SESSION['username']); ?></p>
                <p>الدور: <?php echo $_SESSION['role'] == 'admin' ? 'مدير' : 'مستخدم'; ?></p>
            </div>
        </div>
    </div>
    
    <div class="footer-bottom">
        <div class="footer-bottom-content">
            <p>&copy; <?php echo date('Y'); ?> نظام إدارة المبيعات. جميع الحقوق محفوظة.</p>
            <div class="footer-actions">
                <button onclick="createBackup()" class="footer-btn" title="إنشاء نسخة احتياطية">
                    💾 نسخة احتياطية
                </button>
                <button onclick="checkUpdates()" class="footer-btn" title="فحص التحديثات">
                    🔄 فحص التحديثات
                </button>
                <button onclick="showSystemStatus()" class="footer-btn" title="حالة النظام">
                    📊 حالة النظام
                </button>
            </div>
        </div>
    </div>
</footer>

<!-- نافذة حالة النظام -->
<div id="systemStatusModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>حالة النظام</h3>
            <button class="modal-close" onclick="closeModal('systemStatusModal')">&times;</button>
        </div>
        <div class="modal-body">
            <div id="systemStatusContent">
                <div class="loading">جاري تحميل معلومات النظام...</div>
            </div>
        </div>
    </div>
</div>

<style>
.footer {
    background: #2c3e50;
    color: white;
    margin-top: 50px;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.footer-section h4 {
    margin: 0 0 15px 0;
    color: #ecf0f1;
    font-size: 1.2rem;
}

.footer-section p {
    margin: 0 0 10px 0;
    color: #bdc3c7;
    line-height: 1.6;
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 8px;
}

.footer-links a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s;
}

.footer-links a:hover {
    color: #3498db;
    text-decoration: none;
}

.footer-stats {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #34495e;
}

.footer-stats span {
    color: #95a5a6;
    font-size: 0.9rem;
}

.system-info p {
    margin: 5px 0;
    font-size: 0.9rem;
    color: #95a5a6;
}

.footer-bottom {
    background: #1a252f;
    border-top: 1px solid #34495e;
}

.footer-bottom-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-bottom p {
    margin: 0;
    color: #95a5a6;
    font-size: 0.9rem;
}

.footer-actions {
    display: flex;
    gap: 10px;
}

.footer-btn {
    background: #3498db;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: background-color 0.3s;
}

.footer-btn:hover {
    background: #2980b9;
}

/* النوافذ المنبثقة */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    animation: fadeIn 0.3s;
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    border-radius: 10px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    animation: slideIn 0.3s;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #333;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #999;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    color: #333;
}

.modal-body {
    padding: 20px;
    color: #333;
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.status-item {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    text-align: center;
}

.status-item h4 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 1rem;
}

.status-value {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.status-value.good {
    color: #27ae60;
}

.status-value.warning {
    color: #f39c12;
}

.status-value.error {
    color: #e74c3c;
}

.status-label {
    font-size: 0.9rem;
    color: #666;
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .footer-content {
        grid-template-columns: 1fr;
        gap: 20px;
        padding: 30px 15px;
    }
    
    .footer-bottom-content {
        flex-direction: column;
        gap: 15px;
        text-align: center;
        padding: 15px;
    }
    
    .footer-actions {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .modal-content {
        width: 95%;
        margin: 10% auto;
    }
    
    .status-grid {
        grid-template-columns: 1fr;
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}
</style>

<script>
// إنشاء نسخة احتياطية
function createBackup() {
    if (!confirm('هل تريد إنشاء نسخة احتياطية من النظام؟')) {
        return;
    }
    
    showNotification('جاري إنشاء النسخة الاحتياطية...', 'info');
    
    fetch('../api/backup.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
        } else {
            showNotification('فشل في إنشاء النسخة الاحتياطية: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
        showNotification('خطأ في إنشاء النسخة الاحتياطية', 'danger');
    });
}

// فحص التحديثات
function checkUpdates() {
    showNotification('جاري فحص التحديثات...', 'info');
    
    fetch('../api/check_updates.php')
    .then(response => response.json())
    .then(data => {
        if (data.hasUpdates) {
            showNotification('يوجد تحديثات متاحة للنظام', 'warning');
        } else {
            showNotification('النظام محدث إلى أحدث إصدار', 'success');
        }
    })
    .catch(error => {
        console.error('خطأ في فحص التحديثات:', error);
        showNotification('خطأ في فحص التحديثات', 'danger');
    });
}

// عرض حالة النظام
function showSystemStatus() {
    const modal = document.getElementById('systemStatusModal');
    const content = document.getElementById('systemStatusContent');
    
    modal.style.display = 'block';
    content.innerHTML = '<div class="loading">جاري تحميل معلومات النظام...</div>';
    
    fetch('../api/system_status.php')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            content.innerHTML = `
                <div class="status-grid">
                    <div class="status-item">
                        <h4>حالة قاعدة البيانات</h4>
                        <div class="status-value ${data.database.status === 'connected' ? 'good' : 'error'}">
                            ${data.database.status === 'connected' ? '✓' : '✗'}
                        </div>
                        <div class="status-label">${data.database.status === 'connected' ? 'متصلة' : 'غير متصلة'}</div>
                    </div>
                    
                    <div class="status-item">
                        <h4>مساحة القرص</h4>
                        <div class="status-value ${data.disk.percentage < 80 ? 'good' : data.disk.percentage < 90 ? 'warning' : 'error'}">
                            ${data.disk.percentage}%
                        </div>
                        <div class="status-label">${data.disk.free} متاح من ${data.disk.total}</div>
                    </div>
                    
                    <div class="status-item">
                        <h4>استخدام الذاكرة</h4>
                        <div class="status-value ${data.memory.percentage < 70 ? 'good' : data.memory.percentage < 85 ? 'warning' : 'error'}">
                            ${data.memory.percentage}%
                        </div>
                        <div class="status-label">${data.memory.used} من ${data.memory.total}</div>
                    </div>
                    
                    <div class="status-item">
                        <h4>إصدار PHP</h4>
                        <div class="status-value good">
                            ${data.php.version}
                        </div>
                        <div class="status-label">متوافق</div>
                    </div>
                    
                    <div class="status-item">
                        <h4>وقت التشغيل</h4>
                        <div class="status-value good">
                            ${data.uptime.days}
                        </div>
                        <div class="status-label">أيام</div>
                    </div>
                    
                    <div class="status-item">
                        <h4>آخر نسخة احتياطية</h4>
                        <div class="status-value ${data.backup.status === 'recent' ? 'good' : 'warning'}">
                            ${data.backup.status === 'recent' ? '✓' : '⚠'}
                        </div>
                        <div class="status-label">${data.backup.last_backup || 'لا توجد'}</div>
                    </div>
                </div>
            `;
        } else {
            content.innerHTML = '<p>خطأ في تحميل معلومات النظام</p>';
        }
    })
    .catch(error => {
        console.error('خطأ في تحميل حالة النظام:', error);
        content.innerHTML = '<p>خطأ في تحميل معلومات النظام</p>';
    });
}

// إغلاق النافذة المنبثقة
function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

// إغلاق النافذة عند النقر خارجها
window.onclick = function(event) {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    });
}

// إغلاق النافذة بمفتاح Escape
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            modal.style.display = 'none';
        });
    }
});
</script>
