<?php
session_start();

require_once '../config/config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول
redirectToLogin();

$sale_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$sale_id) {
    header('Location: index.php');
    exit();
}

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // الحصول على بيانات المبيعة
    $sale = $db->fetch("
        SELECT s.*, c.name as customer_name, c.phone as customer_phone, c.address as customer_address,
               u.username as user_name
        FROM sales s 
        LEFT JOIN customers c ON s.customer_id = c.id 
        LEFT JOIN users u ON s.user_id = u.id
        WHERE s.id = ?
    ", [$sale_id]);
    
    if (!$sale) {
        throw new Exception('الفاتورة غير موجودة');
    }
    
    // الحصول على عناصر الفاتورة
    $sale_items = $db->fetchAll("
        SELECT si.*, p.name as product_name, p.barcode
        FROM sale_items si
        JOIN products p ON si.product_id = p.id
        WHERE si.sale_id = ?
        ORDER BY si.id
    ", [$sale_id]);
    
    // الحصول على إعدادات المتجر
    $shop_settings = [];
    $settings = $db->fetchAll("SELECT setting_key, setting_value FROM settings");
    foreach ($settings as $setting) {
        $shop_settings[$setting['setting_key']] = $setting['setting_value'];
    }
    
} catch (Exception $e) {
    die('خطأ: ' . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طباعة فاتورة #<?php echo $sale['invoice_number']; ?></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            text-align: right;
            background: white;
            color: #333;
            line-height: 1.6;
        }
        
        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: white;
        }
        
        .invoice-header {
            text-align: center;
            border-bottom: 3px solid #667eea;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .shop-name {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .shop-info {
            color: #666;
            font-size: 0.9rem;
        }
        
        .shop-info p {
            margin: 5px 0;
        }
        
        .invoice-title {
            background: #667eea;
            color: white;
            padding: 15px;
            text-align: center;
            font-size: 1.5rem;
            font-weight: bold;
            margin: 20px 0;
        }
        
        .invoice-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .detail-section h3 {
            color: #667eea;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
        }
        
        .detail-label {
            font-weight: 600;
            color: #333;
        }
        
        .detail-value {
            color: #666;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .items-table th {
            background: #667eea;
            color: white;
            padding: 15px 10px;
            text-align: center;
            font-weight: 600;
        }
        
        .items-table td {
            padding: 12px 10px;
            text-align: center;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .items-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .items-table tr:hover {
            background: #e3f2fd;
        }
        
        .product-name {
            text-align: right;
            font-weight: 500;
        }
        
        .totals-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 30px 0;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .total-row:last-child {
            border-bottom: none;
        }
        
        .total-label {
            font-weight: 600;
            color: #333;
        }
        
        .total-value {
            font-weight: 600;
            color: #667eea;
        }
        
        .final-total {
            background: #667eea;
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-size: 1.2rem;
            margin-top: 10px;
        }
        
        .payment-status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            font-weight: 600;
        }
        
        .payment-paid {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .payment-partial {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .payment-unpaid {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .notes-section {
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-right: 4px solid #667eea;
        }
        
        .notes-section h4 {
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .invoice-footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #f0f0f0;
            color: #666;
            font-size: 0.9rem;
        }
        
        .print-actions {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
            text-decoration: none;
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
            text-decoration: none;
            color: white;
        }
        
        /* تنسيق الطباعة */
        @media print {
            .print-actions {
                display: none;
            }
            
            .invoice-container {
                padding: 0;
                box-shadow: none;
            }
            
            body {
                background: white;
            }
            
            .items-table {
                box-shadow: none;
            }
        }
        
        /* التصميم المتجاوب */
        @media (max-width: 768px) {
            .invoice-details {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .items-table {
                font-size: 0.85rem;
            }
            
            .items-table th,
            .items-table td {
                padding: 8px 5px;
            }
            
            .shop-name {
                font-size: 1.5rem;
            }
            
            .invoice-title {
                font-size: 1.2rem;
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <!-- أزرار الطباعة -->
        <div class="print-actions">
            <button onclick="window.print()" class="btn btn-primary">🖨️ طباعة الفاتورة</button>
            <a href="index.php" class="btn btn-secondary">← العودة للمبيعات</a>
        </div>
        
        <!-- رأس الفاتورة -->
        <div class="invoice-header">
            <div class="shop-name"><?php echo htmlspecialchars($shop_settings['shop_name'] ?? 'متجر الإلكترونيات'); ?></div>
            <div class="shop-info">
                <?php if (!empty($shop_settings['shop_phone'])): ?>
                    <p>📞 <?php echo htmlspecialchars($shop_settings['shop_phone']); ?></p>
                <?php endif; ?>
                
                <?php if (!empty($shop_settings['shop_email'])): ?>
                    <p>📧 <?php echo htmlspecialchars($shop_settings['shop_email']); ?></p>
                <?php endif; ?>
                
                <?php if (!empty($shop_settings['shop_address'])): ?>
                    <p>📍 <?php echo htmlspecialchars($shop_settings['shop_address']); ?></p>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- عنوان الفاتورة -->
        <div class="invoice-title">
            فاتورة مبيعات رقم: <?php echo $sale['invoice_number']; ?>
        </div>
        
        <!-- تفاصيل الفاتورة -->
        <div class="invoice-details">
            <div class="detail-section">
                <h3>تفاصيل الفاتورة</h3>
                <div class="detail-row">
                    <span class="detail-label">رقم الفاتورة:</span>
                    <span class="detail-value"><?php echo $sale['invoice_number']; ?></span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">التاريخ:</span>
                    <span class="detail-value"><?php echo formatDate($sale['created_at'], 'd/m/Y'); ?></span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">الوقت:</span>
                    <span class="detail-value"><?php echo formatDate($sale['created_at'], 'H:i'); ?></span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">الموظف:</span>
                    <span class="detail-value"><?php echo htmlspecialchars($sale['user_name']); ?></span>
                </div>
            </div>
            
            <div class="detail-section">
                <h3>بيانات العميل</h3>
                <div class="detail-row">
                    <span class="detail-label">اسم العميل:</span>
                    <span class="detail-value"><?php echo $sale['customer_name'] ? htmlspecialchars($sale['customer_name']) : 'عميل نقدي'; ?></span>
                </div>
                <?php if ($sale['customer_phone']): ?>
                    <div class="detail-row">
                        <span class="detail-label">رقم الهاتف:</span>
                        <span class="detail-value"><?php echo htmlspecialchars($sale['customer_phone']); ?></span>
                    </div>
                <?php endif; ?>
                <?php if ($sale['customer_address']): ?>
                    <div class="detail-row">
                        <span class="detail-label">العنوان:</span>
                        <span class="detail-value"><?php echo htmlspecialchars($sale['customer_address']); ?></span>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- جدول العناصر -->
        <table class="items-table">
            <thead>
                <tr>
                    <th>#</th>
                    <th>المنتج</th>
                    <th>الباركود</th>
                    <th>الكمية</th>
                    <th>السعر</th>
                    <th>الإجمالي</th>
                </tr>
            </thead>
            <tbody>
                <?php $counter = 1; ?>
                <?php foreach ($sale_items as $item): ?>
                    <tr>
                        <td><?php echo $counter++; ?></td>
                        <td class="product-name"><?php echo htmlspecialchars($item['product_name']); ?></td>
                        <td><?php echo $item['barcode'] ? htmlspecialchars($item['barcode']) : '-'; ?></td>
                        <td><?php echo $item['quantity']; ?></td>
                        <td><?php echo formatMoney($item['unit_price']); ?></td>
                        <td><?php echo formatMoney($item['total_price']); ?></td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        
        <!-- الإجماليات -->
        <div class="totals-section">
            <?php
            $subtotal = array_sum(array_column($sale_items, 'total_price'));
            $remaining = $sale['total_amount'] - $sale['paid_amount'];
            ?>
            
            <div class="total-row">
                <span class="total-label">المجموع الفرعي:</span>
                <span class="total-value"><?php echo formatMoney($subtotal); ?></span>
            </div>
            
            <?php if ($sale['discount'] > 0): ?>
                <div class="total-row">
                    <span class="total-label">الخصم:</span>
                    <span class="total-value">- <?php echo formatMoney($sale['discount']); ?></span>
                </div>
            <?php endif; ?>
            
            <div class="total-row final-total">
                <span class="total-label">الإجمالي النهائي:</span>
                <span class="total-value"><?php echo formatMoney($sale['total_amount']); ?></span>
            </div>
            
            <div class="total-row">
                <span class="total-label">المبلغ المدفوع:</span>
                <span class="total-value"><?php echo formatMoney($sale['paid_amount']); ?></span>
            </div>
            
            <?php if ($remaining != 0): ?>
                <div class="total-row">
                    <span class="total-label"><?php echo $remaining > 0 ? 'المتبقي:' : 'الباقي للعميل:'; ?></span>
                    <span class="total-value"><?php echo formatMoney(abs($remaining)); ?></span>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- حالة الدفع -->
        <?php
        $payment_class = 'payment-paid';
        $payment_text = 'مدفوعة بالكامل';
        
        if ($remaining > 0) {
            $payment_class = $sale['paid_amount'] > 0 ? 'payment-partial' : 'payment-unpaid';
            $payment_text = $sale['paid_amount'] > 0 ? 'مدفوعة جزئياً' : 'غير مدفوعة';
        } elseif ($remaining < 0) {
            $payment_text = 'مدفوعة مع باقي للعميل';
        }
        ?>
        
        <div class="payment-status <?php echo $payment_class; ?>">
            حالة الدفع: <?php echo $payment_text; ?>
        </div>
        
        <!-- الملاحظات -->
        <?php if (!empty($sale['notes'])): ?>
            <div class="notes-section">
                <h4>ملاحظات:</h4>
                <p><?php echo nl2br(htmlspecialchars($sale['notes'])); ?></p>
            </div>
        <?php endif; ?>
        
        <!-- تذييل الفاتورة -->
        <div class="invoice-footer">
            <p>شكراً لتعاملكم معنا</p>
            <p>تم إنشاء هذه الفاتورة بواسطة نظام إدارة المبيعات</p>
            <p>تاريخ الطباعة: <?php echo date('d/m/Y H:i'); ?></p>
        </div>
    </div>
    
    <script>
        // طباعة تلقائية عند تحميل الصفحة (اختياري)
        // window.onload = function() {
        //     window.print();
        // };
        
        // إضافة اختصار لوحة المفاتيح للطباعة
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                window.print();
            }
        });
    </script>
</body>
</html>
