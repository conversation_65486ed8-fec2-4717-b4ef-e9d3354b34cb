<?php
// ملف فحص الاتصال بقاعدة البيانات

// التحقق من وجود ملف الإعدادات
if (!file_exists('config/config.php')) {
    die('ملف الإعدادات غير موجود. يرجى تشغيل install.php أولاً.');
}

require_once 'config/config.php';

echo "<h2>فحص الاتصال بقاعدة البيانات</h2>";

echo "<p><strong>إعدادات قاعدة البيانات:</strong></p>";
echo "<ul>";
echo "<li>الخادم: " . (defined('DB_HOST') ? DB_HOST : 'غير محدد') . "</li>";
echo "<li>قاعدة البيانات: " . (defined('DB_NAME') ? DB_NAME : 'غير محدد') . "</li>";
echo "<li>اسم المستخدم: " . (defined('DB_USER') ? DB_USER : 'غير محدد') . "</li>";
echo "<li>كلمة المرور: " . (defined('DB_PASS') ? (DB_PASS ? 'محددة' : 'فارغة') : 'غير محدد') . "</li>";
echo "</ul>";

try {
    // محاولة الاتصال بقاعدة البيانات
    $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4";
    $pdo = new PDO($dsn, DB_USER, DB_PASS, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
    ]);
    
    echo "<p style='color: green;'><strong>✅ تم الاتصال بقاعدة البيانات بنجاح!</strong></p>";
    
    // فحص الجداول
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<p><strong>الجداول الموجودة (" . count($tables) . " جدول):</strong></p>";
    echo "<ul>";
    foreach ($tables as $table) {
        echo "<li>$table</li>";
    }
    echo "</ul>";
    
    // فحص المستخدمين
    if (in_array('users', $tables)) {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
        $userCount = $stmt->fetch()['count'];
        echo "<p><strong>عدد المستخدمين:</strong> $userCount</p>";
        
        if ($userCount > 0) {
            $stmt = $pdo->query("SELECT username, email, role FROM users LIMIT 5");
            $users = $stmt->fetchAll();
            echo "<p><strong>المستخدمين:</strong></p>";
            echo "<ul>";
            foreach ($users as $user) {
                echo "<li>{$user['username']} ({$user['email']}) - {$user['role']}</li>";
            }
            echo "</ul>";
        }
    }
    
    // فحص الإعدادات
    if (in_array('settings', $tables)) {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM settings");
        $settingsCount = $stmt->fetch()['count'];
        echo "<p><strong>عدد الإعدادات:</strong> $settingsCount</p>";
        
        if ($settingsCount > 0) {
            $stmt = $pdo->query("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('shop_name', 'currency', 'currency_symbol') LIMIT 10");
            $settings = $stmt->fetchAll();
            echo "<p><strong>بعض الإعدادات:</strong></p>";
            echo "<ul>";
            foreach ($settings as $setting) {
                echo "<li>{$setting['setting_key']}: {$setting['setting_value']}</li>";
            }
            echo "</ul>";
        }
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'><strong>❌ فشل الاتصال بقاعدة البيانات:</strong></p>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    
    // اقتراحات لحل المشكلة
    echo "<h3>اقتراحات لحل المشكلة:</h3>";
    echo "<ol>";
    echo "<li>تأكد من أن خادم MySQL يعمل</li>";
    echo "<li>تأكد من صحة اسم المستخدم وكلمة المرور</li>";
    echo "<li>تأكد من وجود قاعدة البيانات</li>";
    echo "<li>تأكد من صلاحيات المستخدم للوصول لقاعدة البيانات</li>";
    echo "<li>جرب تشغيل install.php مرة أخرى</li>";
    echo "</ol>";
    
    // معلومات إضافية للتشخيص
    echo "<h3>معلومات إضافية:</h3>";
    echo "<ul>";
    echo "<li>رمز الخطأ: " . $e->getCode() . "</li>";
    echo "<li>ملف الخطأ: " . $e->getFile() . "</li>";
    echo "<li>سطر الخطأ: " . $e->getLine() . "</li>";
    echo "</ul>";
}

echo "<hr>";
echo "<p><a href='install.php'>العودة للتثبيت</a> | <a href='login.php'>تسجيل الدخول</a></p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    direction: rtl;
    text-align: right;
    margin: 20px;
    background: #f5f5f5;
}

h2 {
    color: #333;
    border-bottom: 2px solid #667eea;
    padding-bottom: 10px;
}

ul {
    background: white;
    padding: 15px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

li {
    margin: 5px 0;
    padding: 5px;
    border-bottom: 1px solid #f0f0f0;
}

li:last-child {
    border-bottom: none;
}

a {
    color: #667eea;
    text-decoration: none;
    padding: 10px 15px;
    background: white;
    border-radius: 5px;
    margin: 0 5px;
    display: inline-block;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

a:hover {
    background: #667eea;
    color: white;
}
</style>
