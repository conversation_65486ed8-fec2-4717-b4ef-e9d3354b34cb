/* ملف CSS خاص بنظام المبيعات */

.sales-interface {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 30px;
    margin-bottom: 30px;
}

.sales-left {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.sales-right {
    position: sticky;
    top: 90px;
    height: fit-content;
}

/* قسم البحث */
.search-section {
    padding: 20px;
}

.barcode-input {
    display: flex;
    gap: 10px;
}

.barcode-input input {
    flex: 1;
}

/* شبكة المنتجات */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 15px;
    padding: 20px;
    max-height: 600px;
    overflow-y: auto;
}

.product-card {
    background: white;
    border: 2px solid #f0f0f0;
    border-radius: 10px;
    padding: 15px;
    transition: all 0.3s;
    cursor: pointer;
}

.product-card:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
}

.product-info h4 {
    margin: 0 0 8px 0;
    color: #333;
    font-size: 1rem;
}

.product-category {
    color: #666;
    font-size: 0.85rem;
    margin: 0 0 5px 0;
}

.product-price {
    color: #27ae60;
    font-weight: bold;
    font-size: 1.1rem;
    margin: 0 0 5px 0;
}

.product-stock {
    color: #666;
    font-size: 0.85rem;
    margin: 0 0 5px 0;
}

.product-barcode {
    color: #999;
    font-size: 0.8rem;
    margin: 0 0 10px 0;
    font-family: monospace;
}

.product-actions {
    text-align: center;
}

/* بطاقة الفاتورة */
.invoice-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.customer-section {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
}

.customer-info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
}

.info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.info-item strong {
    color: #333;
    font-size: 0.9rem;
}

.info-item span {
    color: #666;
    font-size: 0.95rem;
}

/* جدول الفاتورة */
.invoice-items {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
}

#invoice-table {
    margin: 0;
}

#invoice-table th {
    background: #f8f9fa;
    color: #333;
    font-weight: 600;
    padding: 12px 8px;
    font-size: 0.9rem;
}

#invoice-table td {
    padding: 12px 8px;
    vertical-align: middle;
}

.quantity-input {
    width: 70px;
    padding: 5px;
    text-align: center;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.item-price,
.item-total {
    font-weight: 600;
    color: #27ae60;
}

.remove-item {
    background: #dc3545;
    color: white;
    border: none;
    padding: 5px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
}

.remove-item:hover {
    background: #c82333;
}

.empty-invoice {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.empty-invoice p {
    margin: 5px 0;
}

/* إجماليات الفاتورة */
.invoice-totals {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
}

.totals-summary {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
}

.total-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.total-row:last-child {
    border-bottom: none;
}

.total-final {
    font-size: 1.2rem;
    font-weight: bold;
    color: #333;
    background: white;
    margin: 10px -15px;
    padding: 15px;
    border-radius: 8px;
}

.remaining {
    color: #dc3545;
    font-weight: 600;
}

/* أزرار الإجراءات */
.invoice-actions {
    padding: 20px;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.invoice-actions .btn {
    flex: 1;
    min-width: 120px;
}

/* الشارات */
.badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
}

.badge-success {
    background: #d4edda;
    color: #155724;
}

.badge-warning {
    background: #fff3cd;
    color: #856404;
}

.badge-danger {
    background: #f8d7da;
    color: #721c24;
}

/* تأثيرات الأنيميشن */
.product-card.adding {
    animation: pulse 0.5s;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.invoice-item-new {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* التصميم المتجاوب */
@media (max-width: 1200px) {
    .sales-interface {
        grid-template-columns: 1fr 350px;
    }
}

@media (max-width: 992px) {
    .sales-interface {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .sales-right {
        position: static;
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        max-height: 400px;
    }
}

@media (max-width: 768px) {
    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 10px;
        padding: 15px;
    }
    
    .product-card {
        padding: 12px;
    }
    
    .product-info h4 {
        font-size: 0.9rem;
    }
    
    .barcode-input {
        flex-direction: column;
    }
    
    .form-row {
        flex-direction: column;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .invoice-actions {
        flex-direction: column;
    }
    
    .invoice-actions .btn {
        width: 100%;
    }
    
    #invoice-table {
        font-size: 0.85rem;
    }
    
    #invoice-table th,
    #invoice-table td {
        padding: 8px 4px;
    }
    
    .quantity-input {
        width: 50px;
    }
}

@media (max-width: 480px) {
    .products-grid {
        grid-template-columns: 1fr;
    }
    
    .customer-section,
    .invoice-items,
    .invoice-totals,
    .invoice-actions {
        padding: 15px;
    }
    
    .totals-summary {
        padding: 12px;
    }
    
    .total-final {
        margin: 10px -12px;
        padding: 12px;
    }
}

/* حالات خاصة */
.out-of-stock {
    opacity: 0.6;
    pointer-events: none;
}

.out-of-stock .product-card {
    background: #f8f9fa;
    border-color: #dee2e6;
}

.low-stock {
    border-left: 4px solid #ffc107;
}

.low-stock .product-stock {
    color: #856404;
    font-weight: 600;
}

/* تحسينات إضافية */
.search-highlight {
    background: yellow;
    padding: 2px 4px;
    border-radius: 3px;
}

.customer-balance-positive {
    color: #28a745;
}

.customer-balance-negative {
    color: #dc3545;
}

.invoice-total-zero {
    color: #6c757d;
}

.invoice-total-positive {
    color: #28a745;
}

/* تحسين الطباعة */
@media print {
    .sales-interface {
        display: none;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}
