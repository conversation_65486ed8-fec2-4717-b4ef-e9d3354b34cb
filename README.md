# نظام إدارة المبيعات والصيانة

نظام شامل لإدارة المحلات المتخصصة في بيع اكسسوارات الموبايلات والموبايلات والحاسبات وصيانة الموبايلات.

## المميزات الرئيسية

### 🛒 نظام المبيعات
- واجهة مبيعات احترافية مع البحث والباركود
- إدارة العملاء والديون
- فواتير احترافية مع الطباعة
- إرجاع المنتجات بعد البيع

### 🔧 نظام الصيانة
- إدارة طلبات الصيانة
- تتبع حالات الصيانة (معلق، قيد التنفيذ، مكتمل، فاشل، مسلم)
- إدارة قطع الغيار والتكاليف
- طباعة وصولات الصيانة والتسليم

### 👥 إدارة العملاء
- إضافة وتعديل بيانات العملاء
- تسديد الديون وإدارة الحسابات
- عرض سجلات العملاء الكاملة
- ربط العملاء بالفواتير والصيانة

### 📦 إدارة المنتجات
- إضافة وتعديل المنتجات والأسعار
- إدارة المخزون والكميات
- طباعة الباركود بقياسات مخصصة
- تنبيهات المخزون المنخفض

### 📊 التقارير والإحصائيات
- تقارير المبيعات اليومية والشهرية
- تقارير الصيانة والأرباح
- إحصائيات شاملة للأداء
- تصدير التقارير

### ⚙️ الإعدادات والإدارة
- إعدادات المتجر (الاسم، الهاتف، العنوان)
- إدارة المستخدمين والصلاحيات
- النسخ الاحتياطي التلقائي
- سجل شامل لجميع الأنشطة

### 🔔 التنبيهات الذكية
- تنبيهات المخزون المنخفض
- تنبيهات الصيانة المتأخرة (+5 أيام)
- إشعارات النظام المختلفة

### 🖨️ طباعة الباركود
- طباعة باركود بقياس 2.5×4.5 سم
- ترتيب تلقائي على ورق A4
- تضمين اسم المنتج والسعر
- دعم الطباعة المجمعة

## متطلبات التشغيل

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- خادم ويب (Apache/Nginx)
- مكتبة GD لمعالجة الصور
- مكتبة cURL للاتصالات

## التثبيت

1. ارفع الملفات إلى الخادم
2. افتح `install.php` في المتصفح
3. أدخل بيانات قاعدة البيانات
4. أنشئ حساب المدير
5. اكتمل التثبيت وابدأ الاستخدام

## هيكل المشروع

```
sales-system/
├── install.php                 # ملف التثبيت
├── login.php                   # صفحة تسجيل الدخول
├── index.php                   # الصفحة الرئيسية
├── config/                     # إعدادات النظام
│   ├── database.php
│   └── config.php
├── sales/                      # نظام المبيعات
│   ├── index.php
│   ├── sales.css
│   └── sales.js
├── customers/                  # إدارة العملاء
│   ├── index.php
│   ├── customers.css
│   └── customers.js
├── products/                   # إدارة المنتجات
│   ├── index.php
│   ├── products.css
│   └── products.js
├── maintenance/                # نظام الصيانة
│   ├── index.php
│   ├── maintenance.css
│   └── maintenance.js
├── invoices/                   # نظام الفواتير
│   ├── index.php
│   ├── invoices.css
│   └── invoices.js
├── settings/                   # الإعدادات
│   ├── index.php
│   ├── settings.css
│   └── settings.js
├── reports/                    # التقارير
│   ├── index.php
│   ├── reports.css
│   └── reports.js
├── logs/                       # السجلات
│   ├── index.php
│   ├── logs.css
│   └── logs.js
├── notifications/              # التنبيهات
│   ├── index.php
│   ├── notifications.css
│   └── notifications.js
├── assets/                     # الملفات المشتركة
│   ├── css/
│   │   └── main.css
│   ├── js/
│   │   └── main.js
│   └── images/
└── includes/                   # الملفات المشتركة
    ├── header.php
    ├── footer.php
    ├── functions.php
    └── auth.php
```
