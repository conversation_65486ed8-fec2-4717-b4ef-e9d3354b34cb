# نظام إدارة المبيعات والصيانة

نظام شامل لإدارة المحلات المتخصصة في بيع اكسسوارات الموبايلات والموبايلات والحاسبات وصيانة الموبايلات.

## المميزات الرئيسية

- واجهة مبيعات احترافية مع البحث والباركود
- إدارة العملاء والديون
- إدارة المنتجات والمخزون
- نظام صيانة شامل
- تقارير مفصلة
- نظام تنبيهات ذكي
- واجهة متجاوبة لجميع الأجهزة

## متطلبات التشغيل

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- خادم ويب (Apache/Nginx)

## التثبيت

1. ارفع الملفات إلى الخادم
2. افتح install.php في المتصفح
3. اتبع تعليمات التثبيت

## هيكل المشروع

```
sales-system/
├── install.php                 # ملف التثبيت
├── login.php                   # صفحة تسجيل الدخول
├── index.php                   # الصفحة الرئيسية
├── config/                     # إعدادات النظام
│   ├── database.php
│   └── config.php
├── sales/                      # نظام المبيعات
│   ├── index.php
│   ├── sales.css
│   └── sales.js
├── customers/                  # إدارة العملاء
│   ├── index.php
│   ├── customers.css
│   └── customers.js
├── products/                   # إدارة المنتجات
│   ├── index.php
│   ├── products.css
│   └── products.js
├── maintenance/                # نظام الصيانة
│   ├── index.php
│   ├── maintenance.css
│   └── maintenance.js
├── invoices/                   # نظام الفواتير
│   ├── index.php
│   ├── invoices.css
│   └── invoices.js
├── settings/                   # الإعدادات
│   ├── index.php
│   ├── settings.css
│   └── settings.js
├── reports/                    # التقارير
│   ├── index.php
│   ├── reports.css
│   └── reports.js
├── logs/                       # السجلات
│   ├── index.php
│   ├── logs.css
│   └── logs.js
├── notifications/              # التنبيهات
│   ├── index.php
│   ├── notifications.css
│   └── notifications.js
├── assets/                     # الملفات المشتركة
│   ├── css/
│   │   └── main.css
│   ├── js/
│   │   └── main.js
│   └── images/
└── includes/                   # الملفات المشتركة
    ├── header.php
    ├── footer.php
    ├── functions.php
    └── auth.php
```
