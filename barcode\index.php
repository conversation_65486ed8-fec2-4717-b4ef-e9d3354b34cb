<?php
session_start();

require_once '../config/config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول
redirectToLogin();

$error = '';
$success = '';

// الحصول على المنتجات
try {
    $db = new Database();
    
    // البحث
    $search = isset($_GET['search']) ? sanitize($_GET['search']) : '';
    $category_filter = isset($_GET['category']) ? (int)$_GET['category'] : 0;
    
    $where_clause = "WHERE p.is_active = 1 AND p.barcode IS NOT NULL AND p.barcode != ''";
    $params = [];
    
    if (!empty($search)) {
        $where_clause .= " AND (p.name LIKE ? OR p.barcode LIKE ?)";
        $search_param = "%$search%";
        $params = [$search_param, $search_param];
    }
    
    if ($category_filter > 0) {
        $where_clause .= " AND p.category_id = ?";
        $params[] = $category_filter;
    }
    
    // المنتجات
    $products = $db->fetchAll("
        SELECT p.*, c.name as category_name
        FROM products p 
        LEFT JOIN categories c ON p.category_id = c.id 
        $where_clause 
        ORDER BY p.name
    ", $params);
    
    // الفئات
    $categories = $db->fetchAll("SELECT * FROM categories WHERE is_active = 1 ORDER BY name");
    
    // إعدادات الباركود
    $barcode_settings = [
        'width' => 4.5, // سم
        'height' => 2.5, // سم
        'per_row' => 4,
        'per_column' => 10,
        'margin_top' => 1,
        'margin_left' => 1,
        'margin_right' => 1,
        'margin_bottom' => 1
    ];
    
    // الحصول على الإعدادات من قاعدة البيانات
    $settings = $db->fetchAll("SELECT setting_key, setting_value FROM settings WHERE setting_key LIKE 'barcode_%'");
    foreach ($settings as $setting) {
        $key = str_replace('barcode_', '', $setting['setting_key']);
        if (isset($barcode_settings[$key])) {
            $barcode_settings[$key] = (float)$setting['setting_value'];
        }
    }
    
} catch (Exception $e) {
    $error = 'خطأ في تحميل البيانات: ' . $e->getMessage();
    $products = [];
    $categories = [];
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طباعة الباركود - نظام إدارة المبيعات</title>
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="barcode.css">
</head>
<body>
    <?php include '../includes/header.php'; ?>
    
    <div class="container">
        <div class="page-header">
            <h1>🏷️ طباعة الباركود</h1>
            <p>طباعة ملصقات الباركود للمنتجات بقياس مخصص</p>
        </div>
        
        <?php if ($error): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <?php if ($success): ?>
            <div class="alert alert-success"><?php echo $success; ?></div>
        <?php endif; ?>
        
        <!-- إعدادات الطباعة -->
        <div class="card">
            <div class="card-header">
                <h3>إعدادات الطباعة</h3>
            </div>
            <div class="settings-section">
                <div class="form-row">
                    <div class="form-col">
                        <label class="form-label">عرض الملصق (سم)</label>
                        <input type="number" id="label-width" class="form-control" 
                               value="<?php echo $barcode_settings['width']; ?>" 
                               min="2" max="10" step="0.1">
                    </div>
                    <div class="form-col">
                        <label class="form-label">ارتفاع الملصق (سم)</label>
                        <input type="number" id="label-height" class="form-control" 
                               value="<?php echo $barcode_settings['height']; ?>" 
                               min="1" max="5" step="0.1">
                    </div>
                    <div class="form-col">
                        <label class="form-label">عدد الملصقات في الصف</label>
                        <input type="number" id="labels-per-row" class="form-control" 
                               value="<?php echo $barcode_settings['per_row']; ?>" 
                               min="1" max="6">
                    </div>
                    <div class="form-col">
                        <label class="form-label">عدد الصفوف</label>
                        <input type="number" id="rows-per-page" class="form-control" 
                               value="<?php echo $barcode_settings['per_column']; ?>" 
                               min="1" max="15">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-col">
                        <div class="form-check">
                            <input type="checkbox" id="show-product-name" class="form-check-input" checked>
                            <label for="show-product-name" class="form-check-label">عرض اسم المنتج</label>
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="form-check">
                            <input type="checkbox" id="show-price" class="form-check-input" checked>
                            <label for="show-price" class="form-check-label">عرض السعر</label>
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="form-check">
                            <input type="checkbox" id="show-barcode-text" class="form-check-input" checked>
                            <label for="show-barcode-text" class="form-check-label">عرض رقم الباركود</label>
                        </div>
                    </div>
                </div>
                
                <div class="preview-actions">
                    <button type="button" class="btn btn-info" onclick="updatePreview()">
                        👁️ معاينة
                    </button>
                    <button type="button" class="btn btn-success" onclick="printSelected()">
                        🖨️ طباعة المحدد
                    </button>
                    <button type="button" class="btn btn-warning" onclick="printAll()">
                        🖨️ طباعة الكل
                    </button>
                </div>
            </div>
        </div>
        
        <!-- البحث والفلترة -->
        <div class="card">
            <div class="card-header">
                <h3>اختيار المنتجات</h3>
            </div>
            <div class="search-section">
                <form method="GET" class="search-form">
                    <div class="form-row">
                        <div class="form-col">
                            <input type="text" name="search" class="form-control" 
                                   placeholder="البحث بالاسم أو الباركود..."
                                   value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                        <div class="form-col">
                            <select name="category" class="form-control">
                                <option value="">جميع الفئات</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category['id']; ?>" 
                                            <?php echo $category_filter == $category['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($category['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="form-col-auto">
                            <button type="submit" class="btn btn-primary">🔍 بحث</button>
                            <a href="index.php" class="btn btn-secondary">مسح</a>
                        </div>
                    </div>
                </form>
                
                <div class="bulk-actions">
                    <button type="button" class="btn btn-outline-primary" onclick="selectAll()">
                        تحديد الكل
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="selectNone()">
                        إلغاء التحديد
                    </button>
                    <span class="selected-count">المحدد: <span id="selected-count">0</span></span>
                </div>
            </div>
        </div>
        
        <!-- قائمة المنتجات -->
        <div class="card">
            <div class="card-header">
                <h3>المنتجات المتاحة للطباعة (<?php echo count($products); ?> منتج)</h3>
            </div>
            
            <?php if (empty($products)): ?>
                <div class="empty-state">
                    <div class="empty-icon">🏷️</div>
                    <h3>لا توجد منتجات للطباعة</h3>
                    <p>تأكد من وجود منتجات لها باركود</p>
                    <a href="../products/" class="btn btn-primary">إدارة المنتجات</a>
                </div>
            <?php else: ?>
                <div class="products-grid">
                    <?php foreach ($products as $product): ?>
                        <div class="product-card">
                            <div class="product-select">
                                <input type="checkbox" class="product-checkbox" 
                                       value="<?php echo $product['id']; ?>"
                                       data-name="<?php echo htmlspecialchars($product['name']); ?>"
                                       data-barcode="<?php echo htmlspecialchars($product['barcode']); ?>"
                                       data-price="<?php echo $product['selling_price']; ?>"
                                       onchange="updateSelectedCount()">
                            </div>
                            
                            <div class="product-info">
                                <h4><?php echo htmlspecialchars($product['name']); ?></h4>
                                <p class="product-category"><?php echo htmlspecialchars($product['category_name'] ?: 'غير محدد'); ?></p>
                                <p class="product-barcode">
                                    <code><?php echo htmlspecialchars($product['barcode']); ?></code>
                                </p>
                                <p class="product-price"><?php echo formatMoney($product['selling_price']); ?></p>
                            </div>
                            
                            <div class="product-quantity">
                                <label>عدد النسخ:</label>
                                <input type="number" class="quantity-input" value="1" min="1" max="100">
                            </div>
                            
                            <div class="product-actions">
                                <button type="button" class="btn btn-sm btn-info" 
                                        onclick="previewSingle(<?php echo $product['id']; ?>)">
                                    👁️ معاينة
                                </button>
                                <button type="button" class="btn btn-sm btn-success" 
                                        onclick="printSingle(<?php echo $product['id']; ?>)">
                                    🖨️ طباعة
                                </button>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- معاينة الطباعة -->
        <div class="card" id="print-preview" style="display: none;">
            <div class="card-header">
                <h3>معاينة الطباعة</h3>
                <button type="button" class="btn btn-secondary" onclick="hidePrintPreview()">
                    إخفاء المعاينة
                </button>
            </div>
            <div class="preview-container">
                <div id="preview-content">
                    <!-- سيتم إنشاء المعاينة هنا -->
                </div>
            </div>
        </div>
    </div>
    
    <?php include '../includes/footer.php'; ?>
    
    <script src="../assets/js/main.js"></script>
    <script src="barcode.js"></script>
    
    <!-- مكتبة إنشاء الباركود -->
    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
    
    <script>
        // تهيئة النظام
        document.addEventListener('DOMContentLoaded', function() {
            updateSelectedCount();
            
            // تحديث المعاينة عند تغيير الإعدادات
            const settingsInputs = document.querySelectorAll('#label-width, #label-height, #labels-per-row, #rows-per-page');
            settingsInputs.forEach(input => {
                input.addEventListener('change', updatePreview);
            });
            
            const checkboxes = document.querySelectorAll('#show-product-name, #show-price, #show-barcode-text');
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', updatePreview);
            });
        });
        
        // تحديث عدد المنتجات المحددة
        function updateSelectedCount() {
            const selected = document.querySelectorAll('.product-checkbox:checked').length;
            document.getElementById('selected-count').textContent = selected;
        }
        
        // تحديد جميع المنتجات
        function selectAll() {
            const checkboxes = document.querySelectorAll('.product-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
            });
            updateSelectedCount();
        }
        
        // إلغاء تحديد جميع المنتجات
        function selectNone() {
            const checkboxes = document.querySelectorAll('.product-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            updateSelectedCount();
        }
        
        // تحديث المعاينة
        function updatePreview() {
            const selectedProducts = getSelectedProducts();
            if (selectedProducts.length === 0) {
                showNotification('يرجى تحديد منتج واحد على الأقل', 'warning');
                return;
            }
            
            generatePreview(selectedProducts);
            document.getElementById('print-preview').style.display = 'block';
        }
        
        // الحصول على المنتجات المحددة
        function getSelectedProducts() {
            const selected = [];
            const checkboxes = document.querySelectorAll('.product-checkbox:checked');
            
            checkboxes.forEach(checkbox => {
                const card = checkbox.closest('.product-card');
                const quantity = parseInt(card.querySelector('.quantity-input').value) || 1;
                
                for (let i = 0; i < quantity; i++) {
                    selected.push({
                        id: checkbox.value,
                        name: checkbox.dataset.name,
                        barcode: checkbox.dataset.barcode,
                        price: parseFloat(checkbox.dataset.price)
                    });
                }
            });
            
            return selected;
        }
        
        // إنشاء المعاينة
        function generatePreview(products) {
            const previewContent = document.getElementById('preview-content');
            const labelWidth = parseFloat(document.getElementById('label-width').value);
            const labelHeight = parseFloat(document.getElementById('label-height').value);
            const labelsPerRow = parseInt(document.getElementById('labels-per-row').value);
            const rowsPerPage = parseInt(document.getElementById('rows-per-page').value);
            
            const showName = document.getElementById('show-product-name').checked;
            const showPrice = document.getElementById('show-price').checked;
            const showBarcodeText = document.getElementById('show-barcode-text').checked;
            
            let html = '<div class="barcode-page">';
            let currentRow = 0;
            let currentCol = 0;
            
            products.forEach((product, index) => {
                if (currentCol === 0) {
                    html += '<div class="barcode-row">';
                }
                
                html += `
                    <div class="barcode-label" style="width: ${labelWidth}cm; height: ${labelHeight}cm;">
                        <div class="barcode-content">
                            ${showName ? `<div class="product-name">${product.name}</div>` : ''}
                            <div class="barcode-image">
                                <svg id="barcode-${index}"></svg>
                            </div>
                            ${showBarcodeText ? `<div class="barcode-text">${product.barcode}</div>` : ''}
                            ${showPrice ? `<div class="product-price">${formatMoney(product.price)}</div>` : ''}
                        </div>
                    </div>
                `;
                
                currentCol++;
                
                if (currentCol >= labelsPerRow) {
                    html += '</div>'; // إغلاق الصف
                    currentCol = 0;
                    currentRow++;
                    
                    if (currentRow >= rowsPerPage) {
                        html += '</div><div class="barcode-page">'; // صفحة جديدة
                        currentRow = 0;
                    }
                }
            });
            
            if (currentCol > 0) {
                html += '</div>'; // إغلاق الصف الأخير
            }
            
            html += '</div>'; // إغلاق الصفحة
            
            previewContent.innerHTML = html;
            
            // إنشاء الباركود
            products.forEach((product, index) => {
                const svg = document.getElementById(`barcode-${index}`);
                if (svg) {
                    JsBarcode(svg, product.barcode, {
                        format: "CODE128",
                        width: 1,
                        height: 30,
                        displayValue: false,
                        margin: 0
                    });
                }
            });
        }
        
        // طباعة المنتجات المحددة
        function printSelected() {
            const selectedProducts = getSelectedProducts();
            if (selectedProducts.length === 0) {
                showNotification('يرجى تحديد منتج واحد على الأقل', 'warning');
                return;
            }
            
            generatePreview(selectedProducts);
            
            // إنشاء نافذة طباعة
            const printWindow = window.open('', '_blank');
            const printContent = document.getElementById('preview-content').innerHTML;
            
            printWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>طباعة الباركود</title>
                    <style>
                        ${getBarcodeCSS()}
                    </style>
                </head>
                <body>
                    ${printContent}
                    <script>
                        window.onload = function() {
                            window.print();
                            window.close();
                        };
                    </script>
                </body>
                </html>
            `);
            
            printWindow.document.close();
        }
        
        // طباعة جميع المنتجات
        function printAll() {
            selectAll();
            printSelected();
        }
        
        // طباعة منتج واحد
        function printSingle(productId) {
            // إلغاء تحديد جميع المنتجات
            selectNone();
            
            // تحديد المنتج المطلوب
            const checkbox = document.querySelector(`.product-checkbox[value="${productId}"]`);
            if (checkbox) {
                checkbox.checked = true;
                printSelected();
            }
        }
        
        // معاينة منتج واحد
        function previewSingle(productId) {
            selectNone();
            const checkbox = document.querySelector(`.product-checkbox[value="${productId}"]`);
            if (checkbox) {
                checkbox.checked = true;
                updatePreview();
            }
        }
        
        // إخفاء المعاينة
        function hidePrintPreview() {
            document.getElementById('print-preview').style.display = 'none';
        }
        
        // الحصول على CSS الخاص بالباركود
        function getBarcodeCSS() {
            return `
                @page {
                    size: A4;
                    margin: 1cm;
                }
                
                body {
                    font-family: Arial, sans-serif;
                    margin: 0;
                    padding: 0;
                }
                
                .barcode-page {
                    page-break-after: always;
                }
                
                .barcode-page:last-child {
                    page-break-after: avoid;
                }
                
                .barcode-row {
                    display: flex;
                    margin-bottom: 2mm;
                }
                
                .barcode-label {
                    border: 1px solid #ddd;
                    margin-right: 2mm;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    page-break-inside: avoid;
                }
                
                .barcode-label:last-child {
                    margin-right: 0;
                }
                
                .barcode-content {
                    text-align: center;
                    padding: 1mm;
                    width: 100%;
                    height: 100%;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                }
                
                .product-name {
                    font-size: 8px;
                    font-weight: bold;
                    margin-bottom: 1mm;
                    line-height: 1.2;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
                
                .barcode-image {
                    flex: 1;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                
                .barcode-image svg {
                    max-width: 100%;
                    max-height: 100%;
                }
                
                .barcode-text {
                    font-size: 6px;
                    margin-top: 1mm;
                    font-family: monospace;
                }
                
                .product-price {
                    font-size: 7px;
                    font-weight: bold;
                    margin-top: 1mm;
                }
            `;
        }
        
        // تنسيق المبلغ
        function formatMoney(amount) {
            return parseFloat(amount).toLocaleString('ar-IQ') + ' د.ع';
        }
    </script>
</body>
</html>
