<?php
session_start();

require_once '../config/config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول
redirectToLogin();

$error = '';
$success = '';

// معالجة إضافة طلب صيانة جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'add_maintenance') {
    try {
        $customer_id = !empty($_POST['customer_id']) ? (int)$_POST['customer_id'] : null;
        $device_name = sanitize($_POST['device_name']);
        $device_details = sanitize($_POST['device_details']);
        $problem_description = sanitize($_POST['problem_description']);
        $estimated_cost = (float)$_POST['estimated_cost'];
        $notes = sanitize($_POST['notes']);
        
        if (empty($device_name)) {
            throw new Exception('اسم الجهاز مطلوب');
        }
        
        if (empty($problem_description)) {
            throw new Exception('وصف العطل مطلوب');
        }
        
        if ($estimated_cost < 0) {
            throw new Exception('التكلفة المقدرة يجب أن تكون أكبر من الصفر');
        }
        
        $db = new Database();
        
        // إنشاء رقم طلب الصيانة
        $maintenance_number = generateMaintenanceNumber();
        
        $maintenance_data = [
            'maintenance_number' => $maintenance_number,
            'customer_id' => $customer_id,
            'device_name' => $device_name,
            'device_details' => $device_details,
            'problem_description' => $problem_description,
            'estimated_cost' => $estimated_cost,
            'status' => 'pending',
            'user_id' => $_SESSION['user_id'],
            'notes' => $notes
        ];
        
        $maintenance_id = $db->insert('maintenance_requests', $maintenance_data);
        
        if ($maintenance_id) {
            logActivity('إضافة طلب صيانة', "رقم الطلب: $maintenance_number - الجهاز: $device_name");
            $success = "تم إضافة طلب الصيانة بنجاح - رقم الطلب: $maintenance_number";
            
            // إعادة توجيه لطباعة الوصل
            header("Location: print.php?id=$maintenance_id");
            exit();
        } else {
            throw new Exception('فشل في إضافة طلب الصيانة');
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// الحصول على طلبات الصيانة
try {
    $db = new Database();
    
    // البحث والفلترة
    $search = isset($_GET['search']) ? sanitize($_GET['search']) : '';
    $status_filter = isset($_GET['status']) ? sanitize($_GET['status']) : '';
    $date_from = isset($_GET['date_from']) ? sanitize($_GET['date_from']) : '';
    $date_to = isset($_GET['date_to']) ? sanitize($_GET['date_to']) : '';
    
    $where_clause = "WHERE 1=1";
    $params = [];
    
    if (!empty($search)) {
        $where_clause .= " AND (m.maintenance_number LIKE ? OR m.device_name LIKE ? OR c.name LIKE ? OR m.problem_description LIKE ?)";
        $search_param = "%$search%";
        $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param]);
    }
    
    if (!empty($status_filter)) {
        $where_clause .= " AND m.status = ?";
        $params[] = $status_filter;
    }
    
    if (!empty($date_from)) {
        $where_clause .= " AND DATE(m.created_at) >= ?";
        $params[] = $date_from;
    }
    
    if (!empty($date_to)) {
        $where_clause .= " AND DATE(m.created_at) <= ?";
        $params[] = $date_to;
    }
    
    // ترتيب وترقيم الصفحات
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $per_page = 20;
    $offset = ($page - 1) * $per_page;
    
    // العدد الإجمالي
    $total_requests = $db->fetch("
        SELECT COUNT(*) as count 
        FROM maintenance_requests m 
        LEFT JOIN customers c ON m.customer_id = c.id 
        $where_clause
    ", $params)['count'];
    $total_pages = ceil($total_requests / $per_page);
    
    // طلبات الصيانة
    $maintenance_requests = $db->fetchAll("
        SELECT m.*, c.name as customer_name, c.phone as customer_phone,
               u.username as user_name,
               DATEDIFF(NOW(), m.created_at) as days_since_created
        FROM maintenance_requests m 
        LEFT JOIN customers c ON m.customer_id = c.id 
        LEFT JOIN users u ON m.user_id = u.id
        $where_clause 
        ORDER BY m.created_at DESC 
        LIMIT $per_page OFFSET $offset
    ", $params);
    
    // العملاء للاختيار
    $customers = $db->fetchAll("
        SELECT * FROM customers 
        WHERE is_active = 1 
        ORDER BY name
    ");
    
    // إحصائيات الصيانة
    $stats = [
        'total' => $db->fetch("SELECT COUNT(*) as count FROM maintenance_requests")['count'],
        'pending' => $db->fetch("SELECT COUNT(*) as count FROM maintenance_requests WHERE status = 'pending'")['count'],
        'in_progress' => $db->fetch("SELECT COUNT(*) as count FROM maintenance_requests WHERE status = 'in_progress'")['count'],
        'completed' => $db->fetch("SELECT COUNT(*) as count FROM maintenance_requests WHERE status = 'completed'")['count'],
        'failed' => $db->fetch("SELECT COUNT(*) as count FROM maintenance_requests WHERE status = 'failed'")['count'],
        'delivered' => $db->fetch("SELECT COUNT(*) as count FROM maintenance_requests WHERE status = 'delivered'")['count'],
        'overdue' => $db->fetch("SELECT COUNT(*) as count FROM maintenance_requests WHERE status IN ('pending', 'in_progress') AND DATEDIFF(NOW(), created_at) > 5")['count'],
        'total_revenue' => $db->fetch("SELECT COALESCE(SUM(actual_cost), 0) as total FROM maintenance_requests WHERE status = 'delivered' AND actual_cost IS NOT NULL")['total']
    ];
    
} catch (Exception $e) {
    $error = 'خطأ في تحميل البيانات: ' . $e->getMessage();
    $maintenance_requests = [];
    $customers = [];
    $stats = ['total' => 0, 'pending' => 0, 'in_progress' => 0, 'completed' => 0, 'failed' => 0, 'delivered' => 0, 'overdue' => 0, 'total_revenue' => 0];
    $total_pages = 1;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الصيانة - نظام إدارة المبيعات</title>
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="maintenance.css">
</head>
<body>
    <?php include '../includes/header.php'; ?>
    
    <div class="container">
        <div class="page-header">
            <h1>🔧 إدارة الصيانة</h1>
            <p>إدارة طلبات الصيانة وتتبع حالاتها</p>
        </div>
        
        <?php if ($error): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <?php if ($success): ?>
            <div class="alert alert-success"><?php echo $success; ?></div>
        <?php endif; ?>
        
        <!-- إحصائيات الصيانة -->
        <div class="stats-grid">
            <div class="stat-card total">
                <div class="stat-icon">🔧</div>
                <div class="stat-info">
                    <div class="stat-number"><?php echo $stats['total']; ?></div>
                    <div class="stat-label">إجمالي الطلبات</div>
                </div>
            </div>
            
            <div class="stat-card pending">
                <div class="stat-icon">⏳</div>
                <div class="stat-info">
                    <div class="stat-number"><?php echo $stats['pending']; ?></div>
                    <div class="stat-label">معلقة</div>
                </div>
            </div>
            
            <div class="stat-card in-progress">
                <div class="stat-icon">🔄</div>
                <div class="stat-info">
                    <div class="stat-number"><?php echo $stats['in_progress']; ?></div>
                    <div class="stat-label">قيد التنفيذ</div>
                </div>
            </div>
            
            <div class="stat-card completed">
                <div class="stat-icon">✅</div>
                <div class="stat-info">
                    <div class="stat-number"><?php echo $stats['completed']; ?></div>
                    <div class="stat-label">مكتملة</div>
                </div>
            </div>
            
            <div class="stat-card failed">
                <div class="stat-icon">❌</div>
                <div class="stat-info">
                    <div class="stat-number"><?php echo $stats['failed']; ?></div>
                    <div class="stat-label">فاشلة</div>
                </div>
            </div>
            
            <div class="stat-card delivered">
                <div class="stat-icon">📦</div>
                <div class="stat-info">
                    <div class="stat-number"><?php echo $stats['delivered']; ?></div>
                    <div class="stat-label">مسلمة</div>
                </div>
            </div>
            
            <div class="stat-card overdue">
                <div class="stat-icon">⚠️</div>
                <div class="stat-info">
                    <div class="stat-number"><?php echo $stats['overdue']; ?></div>
                    <div class="stat-label">متأخرة (+5 أيام)</div>
                </div>
            </div>
            
            <div class="stat-card revenue">
                <div class="stat-icon">💰</div>
                <div class="stat-info">
                    <div class="stat-number"><?php echo formatMoney($stats['total_revenue']); ?></div>
                    <div class="stat-label">إجمالي الإيرادات</div>
                </div>
            </div>
        </div>
        
        <!-- أدوات البحث والفلترة -->
        <div class="card">
            <div class="card-header">
                <h3>البحث والفلترة</h3>
            </div>
            <div class="tools-section">
                <form method="GET" class="filters-form">
                    <div class="form-row">
                        <div class="form-col">
                            <input type="text" name="search" class="form-control" 
                                   placeholder="البحث برقم الطلب أو اسم الجهاز أو العميل..."
                                   value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                        <div class="form-col">
                            <select name="status" class="form-control">
                                <option value="">جميع الحالات</option>
                                <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>معلق</option>
                                <option value="in_progress" <?php echo $status_filter === 'in_progress' ? 'selected' : ''; ?>>قيد التنفيذ</option>
                                <option value="completed" <?php echo $status_filter === 'completed' ? 'selected' : ''; ?>>مكتمل</option>
                                <option value="failed" <?php echo $status_filter === 'failed' ? 'selected' : ''; ?>>فاشل</option>
                                <option value="delivered" <?php echo $status_filter === 'delivered' ? 'selected' : ''; ?>>مسلم</option>
                            </select>
                        </div>
                        <div class="form-col">
                            <input type="date" name="date_from" class="form-control" 
                                   value="<?php echo htmlspecialchars($date_from); ?>" 
                                   placeholder="من تاريخ">
                        </div>
                        <div class="form-col">
                            <input type="date" name="date_to" class="form-control" 
                                   value="<?php echo htmlspecialchars($date_to); ?>" 
                                   placeholder="إلى تاريخ">
                        </div>
                        <div class="form-col-auto">
                            <button type="submit" class="btn btn-primary">🔍 بحث</button>
                            <a href="index.php" class="btn btn-secondary">مسح</a>
                        </div>
                    </div>
                </form>
                
                <div class="action-buttons">
                    <button type="button" class="btn btn-success" onclick="showAddMaintenanceModal()">
                        ➕ طلب صيانة جديد
                    </button>
                    <a href="reports.php" class="btn btn-info">
                        📊 تقارير الصيانة
                    </a>
                    <button type="button" class="btn btn-warning" onclick="checkOverdueRequests()">
                        ⚠️ فحص المتأخرة
                    </button>
                </div>
            </div>
        </div>
        
        <!-- قائمة طلبات الصيانة -->
        <div class="card">
            <div class="card-header">
                <h3>طلبات الصيانة (<?php echo $total_requests; ?> طلب)</h3>
            </div>
            
            <?php if (empty($maintenance_requests)): ?>
                <div class="empty-state">
                    <div class="empty-icon">🔧</div>
                    <h3>لا توجد طلبات صيانة</h3>
                    <p>ابدأ بإضافة طلب صيانة جديد</p>
                    <button type="button" class="btn btn-primary" onclick="showAddMaintenanceModal()">
                        إضافة طلب صيانة جديد
                    </button>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>رقم الطلب</th>
                                <th>العميل</th>
                                <th>الجهاز</th>
                                <th>العطل</th>
                                <th>التكلفة</th>
                                <th>الحالة</th>
                                <th>التاريخ</th>
                                <th>المدة</th>
                                <th>إجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($maintenance_requests as $index => $request): ?>
                                <tr class="<?php echo $request['days_since_created'] > 5 && in_array($request['status'], ['pending', 'in_progress']) ? 'overdue-row' : ''; ?>">
                                    <td><?php echo $offset + $index + 1; ?></td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($request['maintenance_number']); ?></strong>
                                    </td>
                                    <td>
                                        <?php if ($request['customer_name']): ?>
                                            <strong><?php echo htmlspecialchars($request['customer_name']); ?></strong>
                                            <?php if ($request['customer_phone']): ?>
                                                <br><small class="text-muted"><?php echo htmlspecialchars($request['customer_phone']); ?></small>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <span class="text-muted">غير محدد</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($request['device_name']); ?></strong>
                                        <?php if (!empty($request['device_details'])): ?>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($request['device_details']); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="problem-description" title="<?php echo htmlspecialchars($request['problem_description']); ?>">
                                            <?php echo htmlspecialchars(substr($request['problem_description'], 0, 50)) . (strlen($request['problem_description']) > 50 ? '...' : ''); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($request['actual_cost']): ?>
                                            <strong class="text-success"><?php echo formatMoney($request['actual_cost']); ?></strong>
                                            <?php if ($request['estimated_cost'] != $request['actual_cost']): ?>
                                                <br><small class="text-muted">مقدر: <?php echo formatMoney($request['estimated_cost']); ?></small>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <span class="text-warning"><?php echo formatMoney($request['estimated_cost']); ?></span>
                                            <br><small class="text-muted">مقدر</small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                        $status_classes = [
                                            'pending' => 'status-pending',
                                            'in_progress' => 'status-in-progress',
                                            'completed' => 'status-completed',
                                            'failed' => 'status-failed',
                                            'delivered' => 'status-delivered'
                                        ];
                                        $status_labels = [
                                            'pending' => 'معلق',
                                            'in_progress' => 'قيد التنفيذ',
                                            'completed' => 'مكتمل',
                                            'failed' => 'فاشل',
                                            'delivered' => 'مسلم'
                                        ];
                                        ?>
                                        <span class="status-badge <?php echo $status_classes[$request['status']] ?? ''; ?>">
                                            <?php echo $status_labels[$request['status']] ?? $request['status']; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php echo formatDate($request['created_at'], 'd/m/Y'); ?>
                                        <br><small class="text-muted"><?php echo formatDate($request['created_at'], 'H:i'); ?></small>
                                    </td>
                                    <td>
                                        <span class="<?php echo $request['days_since_created'] > 5 ? 'text-danger' : 'text-muted'; ?>">
                                            <?php echo $request['days_since_created']; ?> يوم
                                        </span>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="view.php?id=<?php echo $request['id']; ?>" 
                                               class="btn btn-sm btn-info" title="عرض التفاصيل">
                                                👁️
                                            </a>
                                            <a href="update.php?id=<?php echo $request['id']; ?>" 
                                               class="btn btn-sm btn-warning" title="تحديث الحالة">
                                                ✏️
                                            </a>
                                            <a href="print.php?id=<?php echo $request['id']; ?>" 
                                               class="btn btn-sm btn-secondary" title="طباعة الوصل" target="_blank">
                                                🖨️
                                            </a>
                                            <?php if ($request['status'] === 'completed'): ?>
                                                <a href="delivery.php?id=<?php echo $request['id']; ?>" 
                                                   class="btn btn-sm btn-success" title="تسليم الجهاز">
                                                    📦
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- ترقيم الصفحات -->
                <?php if ($total_pages > 1): ?>
                    <div class="pagination">
                        <?php
                        $query_params = http_build_query(array_filter([
                            'search' => $search,
                            'status' => $status_filter,
                            'date_from' => $date_from,
                            'date_to' => $date_to
                        ]));
                        ?>
                        <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                            <a href="?page=<?php echo $i; ?><?php echo $query_params ? '&' . $query_params : ''; ?>" 
                               class="page-link <?php echo $i == $page ? 'active' : ''; ?>">
                                <?php echo $i; ?>
                            </a>
                        <?php endfor; ?>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- نافذة إضافة طلب صيانة -->
    <div id="addMaintenanceModal" class="modal">
        <div class="modal-content large">
            <div class="modal-header">
                <h3>إضافة طلب صيانة جديد</h3>
                <button class="modal-close" onclick="closeModal('addMaintenanceModal')">&times;</button>
            </div>
            <form method="POST" id="addMaintenanceForm">
                <input type="hidden" name="action" value="add_maintenance">
                <div class="modal-body">
                    <div class="form-row">
                        <div class="form-col">
                            <label class="form-label">العميل</label>
                            <select name="customer_id" id="customer-select" class="form-control" onchange="loadCustomerInfo()">
                                <option value="">عميل جديد / غير محدد</option>
                                <?php foreach ($customers as $customer): ?>
                                    <option value="<?php echo $customer['id']; ?>" 
                                            data-phone="<?php echo $customer['phone']; ?>"
                                            data-address="<?php echo $customer['address']; ?>">
                                        <?php echo htmlspecialchars($customer['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="form-col">
                            <button type="button" class="btn btn-info btn-sm" onclick="showAddCustomerModal()">
                                👤 عميل جديد
                            </button>
                        </div>
                    </div>
                    
                    <div id="customer-info" class="customer-info" style="display: none;">
                        <div class="info-grid">
                            <div class="info-item">
                                <strong>الهاتف:</strong>
                                <span id="customer-phone">-</span>
                            </div>
                            <div class="info-item">
                                <strong>العنوان:</strong>
                                <span id="customer-address">-</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-col">
                            <label class="form-label">اسم الجهاز *</label>
                            <input type="text" name="device_name" class="form-control" required 
                                   placeholder="مثال: iPhone 12 Pro">
                        </div>
                        <div class="form-col">
                            <label class="form-label">تفاصيل الجهاز</label>
                            <input type="text" name="device_details" class="form-control" 
                                   placeholder="اللون، الحالة، الملحقات...">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">وصف العطل *</label>
                        <textarea name="problem_description" class="form-control" rows="3" required
                                  placeholder="اشرح المشكلة بالتفصيل..."></textarea>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-col">
                            <label class="form-label">التكلفة المقدرة</label>
                            <input type="number" name="estimated_cost" class="form-control" 
                                   min="0" step="0.01" value="0">
                        </div>
                        <div class="form-col">
                            <label class="form-label">ملاحظات إضافية</label>
                            <textarea name="notes" class="form-control" rows="2"
                                      placeholder="ملاحظات أو تعليمات خاصة..."></textarea>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-success">💾 حفظ وطباعة الوصل</button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('addMaintenanceModal')">إلغاء</button>
                </div>
            </form>
        </div>
    </div>
    
    <?php include '../includes/footer.php'; ?>
    
    <script src="../assets/js/main.js"></script>
    <script src="maintenance.js"></script>
</body>
</html>
