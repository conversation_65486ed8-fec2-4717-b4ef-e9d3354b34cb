<?php
session_start();

require_once '../config/config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول
redirectToLogin();

$error = '';
$success = '';

// معالجة إضافة منتج جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'add_product') {
    try {
        $name = sanitize($_POST['name']);
        $description = sanitize($_POST['description']);
        $barcode = sanitize($_POST['barcode']);
        $category_id = !empty($_POST['category_id']) ? (int)$_POST['category_id'] : null;
        $purchase_price = (float)$_POST['purchase_price'];
        $selling_price = (float)$_POST['selling_price'];
        $quantity = (int)$_POST['quantity'];
        $min_quantity = (int)$_POST['min_quantity'];
        
        if (empty($name)) {
            throw new Exception('اسم المنتج مطلوب');
        }
        
        if ($purchase_price < 0 || $selling_price < 0) {
            throw new Exception('الأسعار يجب أن تكون أكبر من الصفر');
        }
        
        if ($selling_price < $purchase_price) {
            throw new Exception('سعر البيع يجب أن يكون أكبر من سعر الشراء');
        }
        
        $db = new Database();
        
        // التحقق من عدم تكرار الباركود
        if (!empty($barcode)) {
            $existing = $db->fetch("SELECT id FROM products WHERE barcode = ? AND is_active = 1", [$barcode]);
            if ($existing) {
                throw new Exception('الباركود مسجل مسبقاً');
            }
        }
        
        $product_data = [
            'name' => $name,
            'description' => $description,
            'barcode' => $barcode,
            'category_id' => $category_id,
            'purchase_price' => $purchase_price,
            'selling_price' => $selling_price,
            'quantity' => $quantity,
            'min_quantity' => $min_quantity,
            'is_active' => 1
        ];
        
        $product_id = $db->insert('products', $product_data);
        
        if ($product_id) {
            // تسجيل حركة المخزون الأولية
            if ($quantity > 0) {
                $stock_data = [
                    'product_id' => $product_id,
                    'movement_type' => 'in',
                    'quantity' => $quantity,
                    'reference_type' => 'initial',
                    'notes' => 'مخزون أولي',
                    'user_id' => $_SESSION['user_id']
                ];
                $db->insert('stock_movements', $stock_data);
            }
            
            logActivity('إضافة منتج', "تم إضافة المنتج: $name");
            $success = 'تم إضافة المنتج بنجاح';
        } else {
            throw new Exception('فشل في إضافة المنتج');
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// معالجة حذف منتج
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    try {
        $product_id = (int)$_GET['delete'];
        $db = new Database();
        
        // التحقق من وجود مبيعات للمنتج
        $sales_count = $db->fetch("SELECT COUNT(*) as count FROM sale_items WHERE product_id = ?", [$product_id])['count'];
        
        if ($sales_count > 0) {
            // إلغاء تفعيل بدلاً من الحذف
            $db->update('products', ['is_active' => 0], 'id = ?', [$product_id]);
            $success = 'تم إلغاء تفعيل المنتج بنجاح';
        } else {
            // حذف نهائي
            $db->delete('products', 'id = ?', [$product_id]);
            $success = 'تم حذف المنتج بنجاح';
        }
        
        logActivity('حذف منتج', "تم حذف المنتج رقم: $product_id");
        
    } catch (Exception $e) {
        $error = 'فشل في حذف المنتج: ' . $e->getMessage();
    }
}

// الحصول على قائمة المنتجات
try {
    $db = new Database();
    
    // البحث والفلترة
    $search = isset($_GET['search']) ? sanitize($_GET['search']) : '';
    $category_filter = isset($_GET['category']) ? (int)$_GET['category'] : 0;
    $stock_filter = isset($_GET['stock']) ? sanitize($_GET['stock']) : '';
    
    $where_clause = "WHERE p.is_active = 1";
    $params = [];
    
    if (!empty($search)) {
        $where_clause .= " AND (p.name LIKE ? OR p.barcode LIKE ? OR p.description LIKE ?)";
        $search_param = "%$search%";
        $params = array_merge($params, [$search_param, $search_param, $search_param]);
    }
    
    if ($category_filter > 0) {
        $where_clause .= " AND p.category_id = ?";
        $params[] = $category_filter;
    }
    
    if ($stock_filter === 'low') {
        $where_clause .= " AND p.quantity <= p.min_quantity";
    } elseif ($stock_filter === 'out') {
        $where_clause .= " AND p.quantity = 0";
    }
    
    // ترتيب وترقيم الصفحات
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $per_page = 20;
    $offset = ($page - 1) * $per_page;
    
    // العدد الإجمالي
    $total_products = $db->fetch("
        SELECT COUNT(*) as count 
        FROM products p 
        LEFT JOIN categories c ON p.category_id = c.id 
        $where_clause
    ", $params)['count'];
    $total_pages = ceil($total_products / $per_page);
    
    // المنتجات
    $products = $db->fetchAll("
        SELECT p.*, c.name as category_name,
               (SELECT COUNT(*) FROM sale_items WHERE product_id = p.id) as sales_count,
               (p.selling_price - p.purchase_price) as profit_per_unit
        FROM products p 
        LEFT JOIN categories c ON p.category_id = c.id 
        $where_clause 
        ORDER BY p.created_at DESC 
        LIMIT $per_page OFFSET $offset
    ", $params);
    
    // الفئات للفلترة
    $categories = $db->fetchAll("SELECT * FROM categories WHERE is_active = 1 ORDER BY name");
    
    // إحصائيات المنتجات
    $stats = [
        'total' => $db->fetch("SELECT COUNT(*) as count FROM products WHERE is_active = 1")['count'],
        'low_stock' => $db->fetch("SELECT COUNT(*) as count FROM products WHERE quantity <= min_quantity AND is_active = 1")['count'],
        'out_of_stock' => $db->fetch("SELECT COUNT(*) as count FROM products WHERE quantity = 0 AND is_active = 1")['count'],
        'total_value' => $db->fetch("SELECT COALESCE(SUM(quantity * purchase_price), 0) as total FROM products WHERE is_active = 1")['total']
    ];
    
} catch (Exception $e) {
    $error = 'خطأ في تحميل البيانات: ' . $e->getMessage();
    $products = [];
    $categories = [];
    $stats = ['total' => 0, 'low_stock' => 0, 'out_of_stock' => 0, 'total_value' => 0];
    $total_pages = 1;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المنتجات - نظام إدارة المبيعات</title>
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="products.css">
</head>
<body>
    <?php include '../includes/header.php'; ?>
    
    <div class="container">
        <div class="page-header">
            <h1>📦 إدارة المنتجات</h1>
            <p>إضافة وإدارة المنتجات والمخزون</p>
        </div>
        
        <?php if ($error): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <?php if ($success): ?>
            <div class="alert alert-success"><?php echo $success; ?></div>
        <?php endif; ?>
        
        <!-- إحصائيات المنتجات -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">📦</div>
                <div class="stat-info">
                    <div class="stat-number"><?php echo $stats['total']; ?></div>
                    <div class="stat-label">إجمالي المنتجات</div>
                </div>
            </div>
            
            <div class="stat-card low-stock">
                <div class="stat-icon">⚠️</div>
                <div class="stat-info">
                    <div class="stat-number"><?php echo $stats['low_stock']; ?></div>
                    <div class="stat-label">مخزون منخفض</div>
                </div>
            </div>
            
            <div class="stat-card out-of-stock">
                <div class="stat-icon">❌</div>
                <div class="stat-info">
                    <div class="stat-number"><?php echo $stats['out_of_stock']; ?></div>
                    <div class="stat-label">نفد المخزون</div>
                </div>
            </div>
            
            <div class="stat-card total-value">
                <div class="stat-icon">💰</div>
                <div class="stat-info">
                    <div class="stat-number"><?php echo formatMoney($stats['total_value']); ?></div>
                    <div class="stat-label">قيمة المخزون</div>
                </div>
            </div>
        </div>
        
        <!-- أدوات البحث والفلترة -->
        <div class="card">
            <div class="card-header">
                <h3>البحث والفلترة</h3>
            </div>
            <div class="tools-section">
                <form method="GET" class="filters-form">
                    <div class="form-row">
                        <div class="form-col">
                            <input type="text" name="search" class="form-control" 
                                   placeholder="البحث بالاسم أو الباركود أو الوصف..."
                                   value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                        <div class="form-col">
                            <select name="category" class="form-control">
                                <option value="">جميع الفئات</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category['id']; ?>" 
                                            <?php echo $category_filter == $category['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($category['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="form-col">
                            <select name="stock" class="form-control">
                                <option value="">جميع المنتجات</option>
                                <option value="low" <?php echo $stock_filter === 'low' ? 'selected' : ''; ?>>مخزون منخفض</option>
                                <option value="out" <?php echo $stock_filter === 'out' ? 'selected' : ''; ?>>نفد المخزون</option>
                            </select>
                        </div>
                        <div class="form-col-auto">
                            <button type="submit" class="btn btn-primary">🔍 بحث</button>
                            <a href="index.php" class="btn btn-secondary">مسح</a>
                        </div>
                    </div>
                </form>
                
                <div class="action-buttons">
                    <button type="button" class="btn btn-success" onclick="showAddProductModal()">
                        ➕ إضافة منتج جديد
                    </button>
                    <a href="categories.php" class="btn btn-info">
                        📂 إدارة الفئات
                    </a>
                    <a href="barcode.php" class="btn btn-warning">
                        🏷️ طباعة الباركود
                    </a>
                    <button type="button" class="btn btn-secondary" onclick="exportProducts()">
                        📊 تصدير
                    </button>
                </div>
            </div>
        </div>
        
        <!-- قائمة المنتجات -->
        <div class="card">
            <div class="card-header">
                <h3>قائمة المنتجات (<?php echo $total_products; ?> منتج)</h3>
            </div>
            
            <?php if (empty($products)): ?>
                <div class="empty-state">
                    <div class="empty-icon">📦</div>
                    <h3>لا توجد منتجات</h3>
                    <p>ابدأ بإضافة منتج جديد</p>
                    <button type="button" class="btn btn-primary" onclick="showAddProductModal()">
                        إضافة منتج جديد
                    </button>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>اسم المنتج</th>
                                <th>الفئة</th>
                                <th>الباركود</th>
                                <th>سعر الشراء</th>
                                <th>سعر البيع</th>
                                <th>الربح</th>
                                <th>المخزون</th>
                                <th>المبيعات</th>
                                <th>إجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($products as $index => $product): ?>
                                <tr class="<?php echo $product['quantity'] <= $product['min_quantity'] ? 'low-stock-row' : ''; ?>">
                                    <td><?php echo $offset + $index + 1; ?></td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($product['name']); ?></strong>
                                        <?php if (!empty($product['description'])): ?>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($product['description']); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($product['category_name'] ?: 'غير محدد'); ?></td>
                                    <td>
                                        <?php if ($product['barcode']): ?>
                                            <code><?php echo htmlspecialchars($product['barcode']); ?></code>
                                        <?php else: ?>
                                            <span class="text-muted">غير محدد</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo formatMoney($product['purchase_price']); ?></td>
                                    <td><?php echo formatMoney($product['selling_price']); ?></td>
                                    <td class="text-success"><?php echo formatMoney($product['profit_per_unit']); ?></td>
                                    <td>
                                        <span class="stock-badge <?php echo $product['quantity'] <= $product['min_quantity'] ? 'low' : 'normal'; ?>">
                                            <?php echo $product['quantity']; ?>
                                        </span>
                                        <small class="text-muted">/ <?php echo $product['min_quantity']; ?></small>
                                    </td>
                                    <td>
                                        <span class="badge badge-info"><?php echo $product['sales_count']; ?></span>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="view.php?id=<?php echo $product['id']; ?>" 
                                               class="btn btn-sm btn-info" title="عرض التفاصيل">
                                                👁️
                                            </a>
                                            <a href="edit.php?id=<?php echo $product['id']; ?>" 
                                               class="btn btn-sm btn-warning" title="تعديل">
                                                ✏️
                                            </a>
                                            <button type="button" class="btn btn-sm btn-success" 
                                                    onclick="adjustStock(<?php echo $product['id']; ?>, '<?php echo htmlspecialchars($product['name']); ?>', <?php echo $product['quantity']; ?>)"
                                                    title="تعديل المخزون">
                                                📊
                                            </button>
                                            <?php if ($product['barcode']): ?>
                                                <a href="barcode.php?product_id=<?php echo $product['id']; ?>" 
                                                   class="btn btn-sm btn-secondary" title="طباعة الباركود" target="_blank">
                                                    🏷️
                                                </a>
                                            <?php endif; ?>
                                            <button type="button" class="btn btn-sm btn-danger" 
                                                    onclick="deleteProduct(<?php echo $product['id']; ?>, '<?php echo htmlspecialchars($product['name']); ?>')"
                                                    title="حذف">
                                                🗑️
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- ترقيم الصفحات -->
                <?php if ($total_pages > 1): ?>
                    <div class="pagination">
                        <?php
                        $query_params = http_build_query(array_filter([
                            'search' => $search,
                            'category' => $category_filter,
                            'stock' => $stock_filter
                        ]));
                        ?>
                        <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                            <a href="?page=<?php echo $i; ?><?php echo $query_params ? '&' . $query_params : ''; ?>" 
                               class="page-link <?php echo $i == $page ? 'active' : ''; ?>">
                                <?php echo $i; ?>
                            </a>
                        <?php endfor; ?>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- نافذة إضافة منتج -->
    <div id="addProductModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>إضافة منتج جديد</h3>
                <button class="modal-close" onclick="closeModal('addProductModal')">&times;</button>
            </div>
            <form method="POST" id="addProductForm">
                <input type="hidden" name="action" value="add_product">
                <div class="modal-body">
                    <div class="form-row">
                        <div class="form-col">
                            <label class="form-label">اسم المنتج *</label>
                            <input type="text" name="name" class="form-control" required>
                        </div>
                        <div class="form-col">
                            <label class="form-label">الفئة</label>
                            <select name="category_id" class="form-control">
                                <option value="">اختر الفئة</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category['id']; ?>">
                                        <?php echo htmlspecialchars($category['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">وصف المنتج</label>
                        <textarea name="description" class="form-control" rows="3"></textarea>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-col">
                            <label class="form-label">الباركود</label>
                            <input type="text" name="barcode" class="form-control">
                        </div>
                        <div class="form-col">
                            <button type="button" class="btn btn-secondary" onclick="generateBarcode()">
                                توليد باركود
                            </button>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-col">
                            <label class="form-label">سعر الشراء *</label>
                            <input type="number" name="purchase_price" class="form-control" 
                                   min="0" step="0.01" required>
                        </div>
                        <div class="form-col">
                            <label class="form-label">سعر البيع *</label>
                            <input type="number" name="selling_price" class="form-control" 
                                   min="0" step="0.01" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-col">
                            <label class="form-label">الكمية الأولية</label>
                            <input type="number" name="quantity" class="form-control" 
                                   min="0" value="0">
                        </div>
                        <div class="form-col">
                            <label class="form-label">الحد الأدنى للمخزون</label>
                            <input type="number" name="min_quantity" class="form-control" 
                                   min="0" value="5">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-success">💾 حفظ المنتج</button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('addProductModal')">إلغاء</button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- نافذة تعديل المخزون -->
    <div id="adjustStockModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>تعديل المخزون</h3>
                <button class="modal-close" onclick="closeModal('adjustStockModal')">&times;</button>
            </div>
            <div class="modal-body">
                <div id="stockAdjustmentContent">
                    <!-- سيتم ملء المحتوى بواسطة JavaScript -->
                </div>
            </div>
        </div>
    </div>
    
    <?php include '../includes/footer.php'; ?>
    
    <script src="../assets/js/main.js"></script>
    <script src="products.js"></script>
</body>
</html>
