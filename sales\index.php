<?php
session_start();

require_once '../config/config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول
redirectToLogin();

$error = '';
$success = '';

// معالجة إضافة مبيعة جديدة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'add_sale') {
    try {
        $db = new Database();
        $conn = $db->getConnection();
        
        $customer_id = !empty($_POST['customer_id']) ? (int)$_POST['customer_id'] : null;
        $products = $_POST['products'] ?? [];
        $quantities = $_POST['quantities'] ?? [];
        $prices = $_POST['prices'] ?? [];
        $discount = (float)($_POST['discount'] ?? 0);
        $paid_amount = (float)($_POST['paid_amount'] ?? 0);
        $notes = sanitize($_POST['notes'] ?? '');
        
        if (empty($products)) {
            throw new Exception('يجب إضافة منتج واحد على الأقل');
        }
        
        // حساب الإجمالي
        $total_amount = 0;
        for ($i = 0; $i < count($products); $i++) {
            $total_amount += (float)$quantities[$i] * (float)$prices[$i];
        }
        $total_amount -= $discount;
        
        if ($total_amount < 0) {
            throw new Exception('إجمالي الفاتورة لا يمكن أن يكون سالباً');
        }
        
        // بدء المعاملة
        $conn->beginTransaction();
        
        // إنشاء الفاتورة
        $invoice_number = generateInvoiceNumber();
        $sale_data = [
            'invoice_number' => $invoice_number,
            'customer_id' => $customer_id,
            'user_id' => $_SESSION['user_id'],
            'total_amount' => $total_amount,
            'paid_amount' => $paid_amount,
            'discount' => $discount,
            'status' => 'completed',
            'notes' => $notes
        ];
        
        $sale_id = $db->insert('sales', $sale_data);
        
        if (!$sale_id) {
            throw new Exception('فشل في إنشاء الفاتورة');
        }
        
        // إضافة تفاصيل المبيعة
        for ($i = 0; $i < count($products); $i++) {
            $product_id = (int)$products[$i];
            $quantity = (int)$quantities[$i];
            $unit_price = (float)$prices[$i];
            $total_price = $quantity * $unit_price;
            
            // إضافة العنصر
            $item_data = [
                'sale_id' => $sale_id,
                'product_id' => $product_id,
                'quantity' => $quantity,
                'unit_price' => $unit_price,
                'total_price' => $total_price
            ];
            
            $db->insert('sale_items', $item_data);
            
            // تحديث المخزون
            $stmt = $conn->prepare("UPDATE products SET quantity = quantity - ? WHERE id = ?");
            $stmt->execute([$quantity, $product_id]);
        }
        
        // تحديث رصيد العميل إذا كان هناك مبلغ متبقي
        if ($customer_id && $paid_amount < $total_amount) {
            $remaining = $total_amount - $paid_amount;
            $stmt = $conn->prepare("UPDATE customers SET balance = balance + ? WHERE id = ?");
            $stmt->execute([$remaining, $customer_id]);
        }
        
        // تأكيد المعاملة
        $conn->commit();
        
        // تسجيل النشاط
        logActivity('إضافة مبيعة', "فاتورة رقم: $invoice_number - المبلغ: " . formatMoney($total_amount));
        
        $success = "تم إنشاء الفاتورة بنجاح - رقم الفاتورة: $invoice_number";
        
        // إعادة توجيه لطباعة الفاتورة
        header("Location: print.php?id=$sale_id");
        exit();
        
    } catch (Exception $e) {
        if (isset($conn)) {
            $conn->rollBack();
        }
        $error = $e->getMessage();
    }
}

// الحصول على المنتجات
try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // المنتجات المتاحة
    $products = $db->fetchAll("
        SELECT p.*, c.name as category_name 
        FROM products p 
        LEFT JOIN categories c ON p.category_id = c.id 
        WHERE p.is_active = 1 AND p.quantity > 0 
        ORDER BY p.name
    ");
    
    // العملاء
    $customers = $db->fetchAll("
        SELECT * FROM customers 
        WHERE is_active = 1 
        ORDER BY name
    ");
    
    // آخر المبيعات
    $recent_sales = $db->fetchAll("
        SELECT s.*, c.name as customer_name 
        FROM sales s 
        LEFT JOIN customers c ON s.customer_id = c.id 
        ORDER BY s.created_at DESC 
        LIMIT 10
    ");
    
} catch (Exception $e) {
    $error = "خطأ في تحميل البيانات: " . $e->getMessage();
    $products = [];
    $customers = [];
    $recent_sales = [];
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المبيعات - نظام إدارة المبيعات</title>
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="sales.css">
</head>
<body>
    <?php include '../includes/header.php'; ?>
    
    <div class="container">
        <div class="page-header">
            <h1>💰 إدارة المبيعات</h1>
            <p>إضافة وإدارة عمليات البيع</p>
        </div>
        
        <?php if ($error): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <?php if ($success): ?>
            <div class="alert alert-success"><?php echo $success; ?></div>
        <?php endif; ?>
        
        <!-- واجهة المبيعات -->
        <div class="sales-interface">
            <div class="sales-left">
                <!-- البحث والباركود -->
                <div class="card">
                    <div class="card-header">
                        <h3>البحث عن المنتجات</h3>
                    </div>
                    <div class="search-section">
                        <div class="form-row">
                            <div class="form-col">
                                <label class="form-label">البحث بالاسم</label>
                                <input type="text" id="product-search" class="form-control" 
                                       placeholder="ابحث عن منتج..." onkeyup="searchProducts()">
                            </div>
                            <div class="form-col">
                                <label class="form-label">البحث بالباركود</label>
                                <div class="barcode-input">
                                    <input type="text" id="barcode-input" class="form-control" 
                                           placeholder="امسح أو أدخل الباركود" onkeypress="handleBarcodeInput(event)">
                                    <button type="button" class="btn btn-primary" onclick="searchByBarcode()">
                                        🔍 بحث
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- قائمة المنتجات -->
                <div class="card">
                    <div class="card-header">
                        <h3>المنتجات المتاحة</h3>
                    </div>
                    <div class="products-grid" id="products-grid">
                        <?php foreach ($products as $product): ?>
                            <div class="product-card" data-name="<?php echo strtolower($product['name']); ?>" 
                                 data-barcode="<?php echo $product['barcode']; ?>">
                                <div class="product-info">
                                    <h4><?php echo htmlspecialchars($product['name']); ?></h4>
                                    <p class="product-category"><?php echo htmlspecialchars($product['category_name']); ?></p>
                                    <p class="product-price"><?php echo formatMoney($product['selling_price']); ?></p>
                                    <p class="product-stock">المخزون: <?php echo $product['quantity']; ?></p>
                                    <?php if ($product['barcode']): ?>
                                        <p class="product-barcode">الباركود: <?php echo $product['barcode']; ?></p>
                                    <?php endif; ?>
                                </div>
                                <div class="product-actions">
                                    <button type="button" class="btn btn-success btn-sm" 
                                            onclick="addToInvoice(<?php echo $product['id']; ?>, '<?php echo htmlspecialchars($product['name']); ?>', <?php echo $product['selling_price']; ?>, <?php echo $product['quantity']; ?>)">
                                        ➕ إضافة
                                    </button>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            
            <div class="sales-right">
                <!-- الفاتورة -->
                <div class="card invoice-card">
                    <div class="card-header">
                        <h3>🧾 فاتورة جديدة</h3>
                        <button type="button" class="btn btn-warning btn-sm" onclick="clearInvoice()">
                            🗑️ مسح الفاتورة
                        </button>
                    </div>
                    
                    <form method="POST" id="sales-form">
                        <input type="hidden" name="action" value="add_sale">
                        
                        <!-- اختيار العميل -->
                        <div class="customer-section">
                            <div class="form-row">
                                <div class="form-col">
                                    <label class="form-label">العميل</label>
                                    <select name="customer_id" id="customer-select" class="form-control" onchange="loadCustomerInfo()">
                                        <option value="">عميل نقدي</option>
                                        <?php foreach ($customers as $customer): ?>
                                            <option value="<?php echo $customer['id']; ?>" 
                                                    data-balance="<?php echo $customer['balance']; ?>"
                                                    data-phone="<?php echo $customer['phone']; ?>"
                                                    data-email="<?php echo $customer['email']; ?>">
                                                <?php echo htmlspecialchars($customer['name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="form-col">
                                    <button type="button" class="btn btn-info btn-sm" onclick="showAddCustomerModal()">
                                        👤 عميل جديد
                                    </button>
                                </div>
                            </div>
                            
                            <div id="customer-info" class="customer-info" style="display: none;">
                                <div class="info-grid">
                                    <div class="info-item">
                                        <strong>الرصيد السابق:</strong>
                                        <span id="customer-balance">0.00 ريال</span>
                                    </div>
                                    <div class="info-item">
                                        <strong>الهاتف:</strong>
                                        <span id="customer-phone">-</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- عناصر الفاتورة -->
                        <div class="invoice-items">
                            <table class="table" id="invoice-table">
                                <thead>
                                    <tr>
                                        <th>المنتج</th>
                                        <th>الكمية</th>
                                        <th>السعر</th>
                                        <th>الإجمالي</th>
                                        <th>إجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="invoice-items">
                                    <!-- سيتم إضافة العناصر هنا -->
                                </tbody>
                            </table>
                            
                            <div class="empty-invoice" id="empty-invoice">
                                <p>لم يتم إضافة أي منتجات بعد</p>
                                <p>ابحث عن المنتجات وأضفها للفاتورة</p>
                            </div>
                        </div>
                        
                        <!-- إجمالي الفاتورة -->
                        <div class="invoice-totals">
                            <div class="form-row">
                                <div class="form-col">
                                    <label class="form-label">الخصم</label>
                                    <input type="number" name="discount" id="discount" class="form-control" 
                                           value="0" min="0" step="0.01" onchange="updateTotals()">
                                </div>
                                <div class="form-col">
                                    <label class="form-label">المبلغ المدفوع</label>
                                    <input type="number" name="paid_amount" id="paid-amount" class="form-control" 
                                           value="0" min="0" step="0.01" onchange="updateTotals()">
                                </div>
                            </div>
                            
                            <div class="totals-summary">
                                <div class="total-row">
                                    <span>المجموع الفرعي:</span>
                                    <span id="subtotal">0.00 ريال</span>
                                </div>
                                <div class="total-row">
                                    <span>الخصم:</span>
                                    <span id="discount-amount">0.00 ريال</span>
                                </div>
                                <div class="total-row total-final">
                                    <span>الإجمالي النهائي:</span>
                                    <span id="final-total">0.00 ريال</span>
                                </div>
                                <div class="total-row">
                                    <span>المبلغ المدفوع:</span>
                                    <span id="paid-display">0.00 ريال</span>
                                </div>
                                <div class="total-row remaining">
                                    <span>المتبقي:</span>
                                    <span id="remaining-amount">0.00 ريال</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- ملاحظات -->
                        <div class="form-group">
                            <label class="form-label">ملاحظات</label>
                            <textarea name="notes" class="form-control" rows="3" 
                                      placeholder="ملاحظات إضافية (اختياري)"></textarea>
                        </div>
                        
                        <!-- أزرار الإجراءات -->
                        <div class="invoice-actions">
                            <button type="submit" class="btn btn-success" id="save-invoice" disabled>
                                💾 حفظ الفاتورة
                            </button>
                            <button type="button" class="btn btn-info" onclick="previewInvoice()">
                                👁️ معاينة
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="clearInvoice()">
                                🗑️ مسح
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- آخر المبيعات -->
        <div class="card mt-3">
            <div class="card-header">
                <h3>📊 آخر المبيعات</h3>
                <a href="list.php" class="btn btn-primary btn-sm">عرض جميع المبيعات</a>
            </div>
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>العميل</th>
                            <th>المبلغ</th>
                            <th>التاريخ</th>
                            <th>الحالة</th>
                            <th>إجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($recent_sales)): ?>
                            <tr>
                                <td colspan="6" class="text-center">لا توجد مبيعات</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($recent_sales as $sale): ?>
                                <tr>
                                    <td><?php echo $sale['invoice_number']; ?></td>
                                    <td><?php echo $sale['customer_name'] ?: 'عميل نقدي'; ?></td>
                                    <td><?php echo formatMoney($sale['total_amount']); ?></td>
                                    <td><?php echo formatDate($sale['created_at'], 'd/m/Y H:i'); ?></td>
                                    <td>
                                        <span class="badge badge-success">مكتملة</span>
                                    </td>
                                    <td>
                                        <a href="print.php?id=<?php echo $sale['id']; ?>" class="btn btn-sm btn-info" target="_blank">
                                            🖨️ طباعة
                                        </a>
                                        <a href="view.php?id=<?php echo $sale['id']; ?>" class="btn btn-sm btn-primary">
                                            👁️ عرض
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <?php include '../includes/footer.php'; ?>
    
    <script src="../assets/js/main.js"></script>
    <script src="sales.js"></script>
</body>
</html>
