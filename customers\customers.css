/* ملف CSS خاص بإدارة العملاء */

/* إحصائيات العملاء */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s;
    display: flex;
    align-items: center;
    gap: 20px;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card.debt {
    border-left: 4px solid #dc3545;
}

.stat-card.credit {
    border-left: 4px solid #28a745;
}

.stat-card.total-debt {
    border-left: 4px solid #ffc107;
}

.stat-icon {
    font-size: 3rem;
    opacity: 0.7;
    min-width: 60px;
}

.stat-info {
    flex: 1;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.stat-label {
    color: #666;
    font-size: 0.9rem;
}

/* أدوات البحث والإضافة */
.tools-section {
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.search-section {
    flex: 1;
    min-width: 300px;
}

.search-form .form-row {
    display: flex;
    gap: 10px;
    align-items: end;
}

.search-form .form-col {
    flex: 1;
}

.search-form .form-col-auto {
    flex: none;
}

.add-section {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* جدول العملاء */
.table {
    font-size: 0.9rem;
}

.table th {
    background: #f8f9fa;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}

.table td {
    vertical-align: middle;
    padding: 12px 8px;
}

.action-buttons {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.action-buttons .btn {
    min-width: 35px;
    padding: 6px 8px;
}

/* الشارات */
.badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    min-width: 25px;
    text-align: center;
}

.badge-info {
    background: #d1ecf1;
    color: #0c5460;
}

.badge-warning {
    background: #fff3cd;
    color: #856404;
}

.badge-success {
    background: #d4edda;
    color: #155724;
}

.badge-danger {
    background: #f8d7da;
    color: #721c24;
}

/* ألوان النصوص */
.text-danger {
    color: #dc3545 !important;
    font-weight: 600;
}

.text-success {
    color: #28a745 !important;
    font-weight: 600;
}

.text-muted {
    color: #6c757d !important;
    font-size: 0.85rem;
}

/* الحالة الفارغة */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

.empty-state h3 {
    margin: 0 0 10px 0;
    color: #333;
}

.empty-state p {
    margin: 0 0 20px 0;
    color: #666;
}

/* ترقيم الصفحات */
.pagination {
    display: flex;
    justify-content: center;
    gap: 5px;
    padding: 20px;
    border-top: 1px solid #f0f0f0;
}

.page-link {
    display: inline-block;
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    color: #667eea;
    text-decoration: none;
    transition: all 0.3s;
}

.page-link:hover {
    background: #667eea;
    color: white;
    text-decoration: none;
}

.page-link.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

/* النوافذ المنبثقة */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    animation: fadeIn 0.3s;
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    border-radius: 10px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    animation: slideIn 0.3s;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #333;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #999;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    color: #333;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #f0f0f0;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* تحسينات النماذج */
.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.form-col {
    flex: 1;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #555;
}

.form-control {
    width: 100%;
    padding: 12px;
    border: 2px solid #e0e0e0;
    border-radius: 5px;
    font-size: 14px;
    transition: border-color 0.3s, box-shadow 0.3s;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .stat-card {
        padding: 20px;
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
    
    .stat-icon {
        font-size: 2.5rem;
    }
    
    .tools-section {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }
    
    .search-section {
        min-width: auto;
    }
    
    .search-form .form-row {
        flex-direction: column;
        gap: 10px;
    }
    
    .add-section {
        justify-content: center;
    }
    
    .table-responsive {
        overflow-x: auto;
    }
    
    .table {
        min-width: 800px;
        font-size: 0.8rem;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 3px;
    }
    
    .action-buttons .btn {
        min-width: auto;
        padding: 4px 6px;
        font-size: 0.8rem;
    }
    
    .modal-content {
        width: 95%;
        margin: 10% auto;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 15px;
    }
    
    .form-row {
        flex-direction: column;
        gap: 0;
    }
}

@media (max-width: 480px) {
    .stat-number {
        font-size: 1.5rem;
    }
    
    .stat-label {
        font-size: 0.8rem;
    }
    
    .page-header h1 {
        font-size: 1.5rem;
    }
    
    .pagination {
        flex-wrap: wrap;
        gap: 3px;
    }
    
    .page-link {
        padding: 6px 10px;
        font-size: 0.9rem;
    }
}

/* تأثيرات الأنيميشن */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* تحسينات إضافية */
.customer-balance-positive {
    color: #28a745;
    font-weight: 600;
}

.customer-balance-negative {
    color: #dc3545;
    font-weight: 600;
}

.customer-balance-zero {
    color: #6c757d;
}

.highlight-search {
    background: yellow;
    padding: 2px 4px;
    border-radius: 3px;
}

/* تحسين الطباعة */
@media print {
    .tools-section,
    .action-buttons,
    .pagination,
    .modal {
        display: none !important;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .table {
        font-size: 0.8rem;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}
