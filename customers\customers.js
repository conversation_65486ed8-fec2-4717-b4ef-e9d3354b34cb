// ملف JavaScript خاص بإدارة العملاء

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeCustomersSystem();
});

// تهيئة نظام العملاء
function initializeCustomersSystem() {
    // تهيئة النماذج
    initializeForms();
    
    // تهيئة البحث
    initializeSearch();
    
    // إضافة مستمعي الأحداث
    addEventListeners();
    
    // تحديث الإحصائيات
    updateStats();
}

// تهيئة النماذج
function initializeForms() {
    const addCustomerForm = document.getElementById('addCustomerForm');
    
    if (addCustomerForm) {
        addCustomerForm.addEventListener('submit', function(e) {
            if (!validateCustomerForm(this)) {
                e.preventDefault();
                return false;
            }
            
            // إضافة مؤشر التحميل
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<span class="loading"></span> جاري الحفظ...';
                submitBtn.disabled = true;
            }
        });
    }
}

// التحقق من صحة نموذج العميل
function validateCustomerForm(form) {
    let isValid = true;
    
    // مسح الأخطاء السابقة
    clearFormErrors(form);
    
    // التحقق من الاسم
    const nameField = form.querySelector('input[name="name"]');
    if (!nameField.value.trim()) {
        showFieldError(nameField, 'اسم العميل مطلوب');
        isValid = false;
    }
    
    // التحقق من رقم الهاتف
    const phoneField = form.querySelector('input[name="phone"]');
    if (phoneField.value.trim() && !validatePhone(phoneField.value)) {
        showFieldError(phoneField, 'رقم الهاتف غير صحيح');
        isValid = false;
    }
    
    // التحقق من البريد الإلكتروني
    const emailField = form.querySelector('input[name="email"]');
    if (emailField.value.trim() && !validateEmail(emailField.value)) {
        showFieldError(emailField, 'البريد الإلكتروني غير صحيح');
        isValid = false;
    }
    
    return isValid;
}

// التحقق من صحة رقم الهاتف
function validatePhone(phone) {
    const phoneRegex = /^[0-9+\-\s()]+$/;
    return phoneRegex.test(phone) && phone.length >= 10;
}

// التحقق من صحة البريد الإلكتروني
function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// عرض خطأ في الحقل
function showFieldError(field, message) {
    field.style.borderColor = '#dc3545';
    
    // إزالة رسالة الخطأ السابقة
    const existingError = field.parentNode.querySelector('.field-error');
    if (existingError) {
        existingError.remove();
    }
    
    // إضافة رسالة خطأ جديدة
    const errorDiv = document.createElement('div');
    errorDiv.className = 'field-error';
    errorDiv.style.color = '#dc3545';
    errorDiv.style.fontSize = '12px';
    errorDiv.style.marginTop = '5px';
    errorDiv.textContent = message;
    
    field.parentNode.appendChild(errorDiv);
}

// مسح أخطاء النموذج
function clearFormErrors(form) {
    const errorElements = form.querySelectorAll('.field-error');
    errorElements.forEach(error => error.remove());
    
    const inputs = form.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.style.borderColor = '#e0e0e0';
    });
}

// تهيئة البحث
function initializeSearch() {
    const searchInput = document.querySelector('input[name="search"]');
    
    if (searchInput) {
        // البحث الفوري
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                highlightSearchResults(this.value);
            }, 300);
        });
    }
}

// تمييز نتائج البحث
function highlightSearchResults(searchTerm) {
    if (!searchTerm) return;
    
    const tableRows = document.querySelectorAll('.table tbody tr');
    
    tableRows.forEach(row => {
        const cells = row.querySelectorAll('td');
        let found = false;
        
        cells.forEach(cell => {
            const originalText = cell.textContent;
            if (originalText.toLowerCase().includes(searchTerm.toLowerCase())) {
                found = true;
                const highlightedText = originalText.replace(
                    new RegExp(searchTerm, 'gi'),
                    match => `<span class="highlight-search">${match}</span>`
                );
                cell.innerHTML = highlightedText;
            }
        });
        
        // إظهار أو إخفاء الصف
        row.style.display = found ? '' : 'none';
    });
}

// إضافة مستمعي الأحداث
function addEventListeners() {
    // إغلاق النوافذ المنبثقة عند النقر خارجها
    window.addEventListener('click', function(event) {
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        });
    });
    
    // إغلاق النوافذ بمفتاح Escape
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                modal.style.display = 'none';
            });
        }
    });
}

// عرض نافذة إضافة عميل
function showAddCustomerModal() {
    const modal = document.getElementById('addCustomerModal');
    const form = document.getElementById('addCustomerForm');
    
    // مسح النموذج
    form.reset();
    clearFormErrors(form);
    
    // إعادة تعيين زر الإرسال
    const submitBtn = form.querySelector('button[type="submit"]');
    if (submitBtn) {
        submitBtn.innerHTML = '💾 حفظ العميل';
        submitBtn.disabled = false;
    }
    
    // عرض النافذة
    modal.style.display = 'block';
    
    // تركيز على حقل الاسم
    const nameField = form.querySelector('input[name="name"]');
    if (nameField) {
        setTimeout(() => nameField.focus(), 100);
    }
}

// إغلاق النافذة المنبثقة
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'none';
    }
}

// حذف عميل
function deleteCustomer(customerId, customerName) {
    if (confirm(`هل أنت متأكد من حذف العميل "${customerName}"؟\n\nملاحظة: إذا كان للعميل مبيعات أو صيانة سابقة، سيتم إلغاء تفعيله بدلاً من الحذف.`)) {
        window.location.href = `index.php?delete=${customerId}`;
    }
}

// تحديث الإحصائيات
function updateStats() {
    // يمكن إضافة تحديث دوري للإحصائيات هنا
    setInterval(function() {
        // تحديث الإحصائيات كل 5 دقائق
        // location.reload();
    }, 300000);
}

// تصدير قائمة العملاء
function exportCustomers() {
    const searchParams = new URLSearchParams(window.location.search);
    const exportUrl = `export.php?type=customers&${searchParams.toString()}`;
    
    // فتح رابط التصدير في نافذة جديدة
    window.open(exportUrl, '_blank');
}

// طباعة قائمة العملاء
function printCustomers() {
    // إخفاء العناصر غير المطلوبة للطباعة
    const elementsToHide = document.querySelectorAll('.tools-section, .action-buttons, .pagination');
    elementsToHide.forEach(element => {
        element.style.display = 'none';
    });
    
    // طباعة الصفحة
    window.print();
    
    // إعادة إظهار العناصر
    elementsToHide.forEach(element => {
        element.style.display = '';
    });
}

// البحث المتقدم
function showAdvancedSearch() {
    // يمكن إضافة نافذة بحث متقدم هنا
    showNotification('البحث المتقدم قيد التطوير', 'info');
}

// إضافة عميل سريع من نافذة منبثقة صغيرة
function showQuickAddCustomer() {
    const name = prompt('اسم العميل:');
    if (!name) return;
    
    const phone = prompt('رقم الهاتف (اختياري):');
    
    // إرسال البيانات عبر AJAX
    const formData = new FormData();
    formData.append('action', 'add_customer');
    formData.append('name', name);
    formData.append('phone', phone || '');
    formData.append('email', '');
    formData.append('address', '');
    formData.append('notes', '');
    
    fetch('index.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.text())
    .then(data => {
        if (data.includes('تم إضافة العميل بنجاح')) {
            showNotification('تم إضافة العميل بنجاح', 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showNotification('فشل في إضافة العميل', 'danger');
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        showNotification('خطأ في إضافة العميل', 'danger');
    });
}

// تحديث رصيد العميل
function updateCustomerBalance(customerId, currentBalance) {
    const newBalance = prompt(`الرصيد الحالي: ${currentBalance}\nأدخل الرصيد الجديد:`);
    
    if (newBalance === null) return;
    
    if (isNaN(newBalance)) {
        showNotification('يرجى إدخال رقم صحيح', 'warning');
        return;
    }
    
    // إرسال التحديث عبر AJAX
    fetch('../api/customers.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'update_balance',
            customer_id: customerId,
            balance: parseFloat(newBalance)
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('تم تحديث الرصيد بنجاح', 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showNotification('فشل في تحديث الرصيد: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        showNotification('خطأ في تحديث الرصيد', 'danger');
    });
}

// فلترة العملاء حسب الحالة
function filterCustomers(filterType) {
    const rows = document.querySelectorAll('.table tbody tr');
    
    rows.forEach(row => {
        const balanceCell = row.querySelector('td:nth-child(5)');
        if (!balanceCell) return;
        
        const balanceText = balanceCell.textContent.trim();
        const hasDebt = balanceCell.querySelector('.text-danger');
        const hasCredit = balanceCell.querySelector('.text-success');
        
        let show = true;
        
        switch (filterType) {
            case 'all':
                show = true;
                break;
            case 'debt':
                show = hasDebt !== null;
                break;
            case 'credit':
                show = hasCredit !== null;
                break;
            case 'zero':
                show = !hasDebt && !hasCredit;
                break;
        }
        
        row.style.display = show ? '' : 'none';
    });
}

// إضافة أزرار الفلترة
function addFilterButtons() {
    const toolsSection = document.querySelector('.tools-section');
    if (!toolsSection) return;
    
    const filterDiv = document.createElement('div');
    filterDiv.className = 'filter-buttons';
    filterDiv.innerHTML = `
        <button type="button" class="btn btn-sm btn-outline-primary" onclick="filterCustomers('all')">الكل</button>
        <button type="button" class="btn btn-sm btn-outline-danger" onclick="filterCustomers('debt')">لديهم ديون</button>
        <button type="button" class="btn btn-sm btn-outline-success" onclick="filterCustomers('credit')">لديهم رصيد</button>
        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="filterCustomers('zero')">رصيد صفر</button>
    `;
    
    toolsSection.appendChild(filterDiv);
}

// تهيئة أزرار الفلترة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    addFilterButtons();
});
