<?php
session_start();

// التحقق من وجود ملف الإعدادات
if (!file_exists('config/config.php')) {
    header('Location: install.php');
    exit();
}

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'includes/functions.php';

// التحقق من تسجيل الدخول
redirectToLogin();

// الحصول على الإحصائيات
try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // إحصائيات اليوم
    $today = date('Y-m-d');
    
    // مبيعات اليوم
    $stmt = $conn->prepare("SELECT COUNT(*) as count, COALESCE(SUM(total_amount), 0) as total FROM sales WHERE DATE(created_at) = ?");
    $stmt->execute([$today]);
    $todaySales = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // طلبات الصيانة اليوم
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM maintenance_requests WHERE DATE(created_at) = ?");
    $stmt->execute([$today]);
    $todayMaintenance = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // إجمالي العملاء
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM customers WHERE is_active = 1");
    $stmt->execute();
    $totalCustomers = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // إجمالي المنتجات
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM products WHERE is_active = 1");
    $stmt->execute();
    $totalProducts = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // المنتجات منخفضة المخزون
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM products WHERE quantity <= min_quantity AND is_active = 1");
    $stmt->execute();
    $lowStockProducts = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // طلبات الصيانة المعلقة
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM maintenance_requests WHERE status = 'pending'");
    $stmt->execute();
    $pendingMaintenance = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // آخر المبيعات
    $stmt = $conn->prepare("
        SELECT s.*, c.name as customer_name 
        FROM sales s 
        LEFT JOIN customers c ON s.customer_id = c.id 
        ORDER BY s.created_at DESC 
        LIMIT 5
    ");
    $stmt->execute();
    $recentSales = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // آخر طلبات الصيانة
    $stmt = $conn->prepare("
        SELECT m.*, c.name as customer_name 
        FROM maintenance_requests m 
        LEFT JOIN customers c ON m.customer_id = c.id 
        ORDER BY m.created_at DESC 
        LIMIT 5
    ");
    $stmt->execute();
    $recentMaintenance = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // إحصائيات الشهر
    $thisMonth = date('Y-m');
    $stmt = $conn->prepare("SELECT COALESCE(SUM(total_amount), 0) as total FROM sales WHERE DATE_FORMAT(created_at, '%Y-%m') = ?");
    $stmt->execute([$thisMonth]);
    $monthlyRevenue = $stmt->fetch(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    error_log("خطأ في الحصول على الإحصائيات: " . $e->getMessage());
    $todaySales = ['count' => 0, 'total' => 0];
    $todayMaintenance = ['count' => 0];
    $totalCustomers = ['count' => 0];
    $totalProducts = ['count' => 0];
    $lowStockProducts = ['count' => 0];
    $pendingMaintenance = ['count' => 0];
    $recentSales = [];
    $recentMaintenance = [];
    $monthlyRevenue = ['total' => 0];
}

// فحص التنبيهات
checkLowStock();
checkOverdueMaintenanceRequests();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الصفحة الرئيسية - نظام إدارة المبيعات</title>
    <link rel="stylesheet" href="assets/css/main.css">
    <style>
        .dashboard-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s;
            position: relative;
            overflow: hidden;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .stat-card.sales::before {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
        }
        
        .stat-card.maintenance::before {
            background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
        }
        
        .stat-card.customers::before {
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
        }
        
        .stat-card.products::before {
            background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%);
        }
        
        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .stat-icon {
            font-size: 2.5rem;
            opacity: 0.7;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 1rem;
        }
        
        .stat-change {
            font-size: 0.9rem;
            margin-top: 10px;
        }
        
        .stat-change.positive {
            color: #4CAF50;
        }
        
        .stat-change.negative {
            color: #f44336;
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .quick-action {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s;
            text-decoration: none;
            color: #333;
        }
        
        .quick-action:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            text-decoration: none;
            color: #333;
        }
        
        .quick-action-icon {
            font-size: 2rem;
            margin-bottom: 10px;
            color: #667eea;
        }
        
        .recent-activities {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }
        
        .activity-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .activity-header {
            padding: 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .activity-body {
            padding: 0;
        }
        
        .activity-item {
            padding: 15px 20px;
            border-bottom: 1px solid #f8f9fa;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-item:hover {
            background: #f8f9fa;
        }
        
        .activity-info h4 {
            margin: 0 0 5px 0;
            font-size: 0.9rem;
            color: #333;
        }
        
        .activity-info p {
            margin: 0;
            font-size: 0.8rem;
            color: #666;
        }
        
        .activity-amount {
            font-weight: bold;
            color: #4CAF50;
        }
        
        .activity-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-completed {
            background: #d4edda;
            color: #155724;
        }
        
        .status-in-progress {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        @media (max-width: 768px) {
            .recent-activities {
                grid-template-columns: 1fr;
            }
            
            .dashboard-stats {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
            
            .quick-actions {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
        }
    </style>
</head>
<body>
    <?php include 'includes/header.php'; ?>
    
    <div class="container">
        <div class="page-header">
            <h1>مرحباً، <?php echo htmlspecialchars($_SESSION['username']); ?></h1>
            <p>نظرة عامة على أداء المتجر اليوم</p>
        </div>
        
        <!-- الإحصائيات الرئيسية -->
        <div class="dashboard-stats">
            <div class="stat-card sales fade-in">
                <div class="stat-header">
                    <div>
                        <div class="stat-number"><?php echo $todaySales['count']; ?></div>
                        <div class="stat-label">مبيعات اليوم</div>
                        <div class="stat-change positive">
                            <?php echo formatMoney($todaySales['total']); ?>
                        </div>
                    </div>
                    <div class="stat-icon">🛒</div>
                </div>
            </div>
            
            <div class="stat-card maintenance fade-in">
                <div class="stat-header">
                    <div>
                        <div class="stat-number"><?php echo $todayMaintenance['count']; ?></div>
                        <div class="stat-label">طلبات صيانة اليوم</div>
                        <div class="stat-change">
                            <?php echo $pendingMaintenance['count']; ?> معلقة
                        </div>
                    </div>
                    <div class="stat-icon">🔧</div>
                </div>
            </div>
            
            <div class="stat-card customers fade-in">
                <div class="stat-header">
                    <div>
                        <div class="stat-number"><?php echo $totalCustomers['count']; ?></div>
                        <div class="stat-label">إجمالي العملاء</div>
                        <div class="stat-change positive">نشط</div>
                    </div>
                    <div class="stat-icon">👥</div>
                </div>
            </div>
            
            <div class="stat-card products fade-in">
                <div class="stat-header">
                    <div>
                        <div class="stat-number"><?php echo $totalProducts['count']; ?></div>
                        <div class="stat-label">إجمالي المنتجات</div>
                        <?php if ($lowStockProducts['count'] > 0): ?>
                            <div class="stat-change negative">
                                <?php echo $lowStockProducts['count']; ?> منخفض المخزون
                            </div>
                        <?php else: ?>
                            <div class="stat-change positive">مخزون جيد</div>
                        <?php endif; ?>
                    </div>
                    <div class="stat-icon">📦</div>
                </div>
            </div>
        </div>
        
        <!-- الإجراءات السريعة -->
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">الإجراءات السريعة</h2>
            </div>
            <div class="quick-actions">
                <a href="sales/" class="quick-action fade-in">
                    <div class="quick-action-icon">🛒</div>
                    <h3>مبيعة جديدة</h3>
                    <p>إضافة عملية بيع جديدة</p>
                </a>
                
                <a href="maintenance/" class="quick-action fade-in">
                    <div class="quick-action-icon">🔧</div>
                    <h3>طلب صيانة</h3>
                    <p>إضافة طلب صيانة جديد</p>
                </a>
                
                <a href="customers/" class="quick-action fade-in">
                    <div class="quick-action-icon">👤</div>
                    <h3>عميل جديد</h3>
                    <p>إضافة عميل جديد</p>
                </a>
                
                <a href="products/" class="quick-action fade-in">
                    <div class="quick-action-icon">📦</div>
                    <h3>منتج جديد</h3>
                    <p>إضافة منتج جديد</p>
                </a>
                
                <a href="reports/" class="quick-action fade-in">
                    <div class="quick-action-icon">📊</div>
                    <h3>التقارير</h3>
                    <p>عرض التقارير والإحصائيات</p>
                </a>
                
                <a href="settings/" class="quick-action fade-in">
                    <div class="quick-action-icon">⚙️</div>
                    <h3>الإعدادات</h3>
                    <p>إعدادات النظام</p>
                </a>
            </div>
        </div>
        
        <!-- الأنشطة الحديثة -->
        <div class="recent-activities">
            <div class="activity-card fade-in">
                <div class="activity-header">
                    <h3>آخر المبيعات</h3>
                    <a href="sales/" class="btn btn-sm btn-primary">عرض الكل</a>
                </div>
                <div class="activity-body">
                    <?php if (empty($recentSales)): ?>
                        <div class="activity-item">
                            <div class="activity-info">
                                <p>لا توجد مبيعات حديثة</p>
                            </div>
                        </div>
                    <?php else: ?>
                        <?php foreach ($recentSales as $sale): ?>
                            <div class="activity-item">
                                <div class="activity-info">
                                    <h4>فاتورة #<?php echo $sale['invoice_number']; ?></h4>
                                    <p>
                                        <?php echo $sale['customer_name'] ? $sale['customer_name'] : 'عميل نقدي'; ?>
                                        - <?php echo formatDate($sale['created_at'], 'd/m/Y H:i'); ?>
                                    </p>
                                </div>
                                <div class="activity-amount">
                                    <?php echo formatMoney($sale['total_amount']); ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="activity-card fade-in">
                <div class="activity-header">
                    <h3>آخر طلبات الصيانة</h3>
                    <a href="maintenance/" class="btn btn-sm btn-primary">عرض الكل</a>
                </div>
                <div class="activity-body">
                    <?php if (empty($recentMaintenance)): ?>
                        <div class="activity-item">
                            <div class="activity-info">
                                <p>لا توجد طلبات صيانة حديثة</p>
                            </div>
                        </div>
                    <?php else: ?>
                        <?php foreach ($recentMaintenance as $maintenance): ?>
                            <div class="activity-item">
                                <div class="activity-info">
                                    <h4><?php echo htmlspecialchars($maintenance['device_name']); ?></h4>
                                    <p>
                                        <?php echo $maintenance['customer_name'] ? $maintenance['customer_name'] : 'غير محدد'; ?>
                                        - <?php echo formatDate($maintenance['created_at'], 'd/m/Y H:i'); ?>
                                    </p>
                                </div>
                                <div>
                                    <?php
                                    $statusClass = '';
                                    $statusText = '';
                                    switch ($maintenance['status']) {
                                        case 'pending':
                                            $statusClass = 'status-pending';
                                            $statusText = 'معلق';
                                            break;
                                        case 'in_progress':
                                            $statusClass = 'status-in-progress';
                                            $statusText = 'قيد التنفيذ';
                                            break;
                                        case 'completed':
                                            $statusClass = 'status-completed';
                                            $statusText = 'مكتمل';
                                            break;
                                        default:
                                            $statusClass = 'status-pending';
                                            $statusText = $maintenance['status'];
                                    }
                                    ?>
                                    <span class="activity-status <?php echo $statusClass; ?>">
                                        <?php echo $statusText; ?>
                                    </span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- إحصائيات إضافية -->
        <div class="card mt-3">
            <div class="card-header">
                <h3 class="card-title">إحصائيات الشهر الحالي</h3>
            </div>
            <div class="grid grid-3">
                <div class="stats-card">
                    <div class="stats-number"><?php echo formatMoney($monthlyRevenue['total']); ?></div>
                    <div class="stats-label">إجمالي المبيعات</div>
                </div>
                
                <div class="stats-card">
                    <div class="stats-number"><?php echo $lowStockProducts['count']; ?></div>
                    <div class="stats-label">منتجات تحتاج تجديد</div>
                </div>
                
                <div class="stats-card">
                    <div class="stats-number"><?php echo $pendingMaintenance['count']; ?></div>
                    <div class="stats-label">طلبات صيانة معلقة</div>
                </div>
            </div>
        </div>
    </div>
    
    <?php include 'includes/footer.php'; ?>
    
    <script src="assets/js/main.js"></script>
    <script>
        // تحديث الإحصائيات كل 5 دقائق
        setInterval(function() {
            location.reload();
        }, 300000);
        
        // إضافة تأثيرات للبطاقات
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.stat-card, .quick-action, .activity-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
