// ملف JavaScript خاص بنظام المبيعات

// متغيرات عامة
let invoiceItems = [];
let itemCounter = 0;

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeSalesSystem();
});

// تهيئة نظام المبيعات
function initializeSalesSystem() {
    // تركيز على حقل البحث
    const searchInput = document.getElementById('product-search');
    if (searchInput) {
        searchInput.focus();
    }
    
    // تهيئة البحث بالباركود
    const barcodeInput = document.getElementById('barcode-input');
    if (barcodeInput) {
        barcodeInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                searchByBarcode();
            }
        });
    }
    
    // تحديث حالة الفاتورة
    updateInvoiceState();
    
    // إضافة مستمعي الأحداث
    addEventListeners();
}

// إضافة مستمعي الأحداث
function addEventListeners() {
    // مراقبة تغييرات الخصم والمبلغ المدفوع
    const discountInput = document.getElementById('discount');
    const paidAmountInput = document.getElementById('paid-amount');
    
    if (discountInput) {
        discountInput.addEventListener('input', updateTotals);
    }
    
    if (paidAmountInput) {
        paidAmountInput.addEventListener('input', updateTotals);
    }
    
    // مراقبة إرسال النموذج
    const salesForm = document.getElementById('sales-form');
    if (salesForm) {
        salesForm.addEventListener('submit', function(e) {
            if (!validateInvoice()) {
                e.preventDefault();
                return false;
            }
        });
    }
}

// البحث عن المنتجات
function searchProducts() {
    const searchTerm = document.getElementById('product-search').value.toLowerCase();
    const productCards = document.querySelectorAll('.product-card');
    
    productCards.forEach(card => {
        const productName = card.dataset.name;
        const productBarcode = card.dataset.barcode || '';
        
        if (productName.includes(searchTerm) || productBarcode.includes(searchTerm)) {
            card.style.display = 'block';
            // تمييز النص المطابق
            highlightSearchTerm(card, searchTerm);
        } else {
            card.style.display = 'none';
        }
    });
}

// تمييز النص المطابق في البحث
function highlightSearchTerm(card, searchTerm) {
    if (!searchTerm) return;
    
    const productName = card.querySelector('h4');
    if (productName) {
        const originalText = productName.textContent;
        const highlightedText = originalText.replace(
            new RegExp(searchTerm, 'gi'),
            match => `<span class="search-highlight">${match}</span>`
        );
        productName.innerHTML = highlightedText;
    }
}

// البحث بالباركود
function searchByBarcode() {
    const barcode = document.getElementById('barcode-input').value.trim();
    if (!barcode) {
        showNotification('يرجى إدخال الباركود', 'warning');
        return;
    }
    
    // البحث في المنتجات المعروضة
    const productCards = document.querySelectorAll('.product-card');
    let found = false;
    
    productCards.forEach(card => {
        if (card.dataset.barcode === barcode) {
            found = true;
            
            // استخراج معلومات المنتج
            const productInfo = extractProductInfo(card);
            
            // إضافة المنتج للفاتورة
            addToInvoice(
                productInfo.id,
                productInfo.name,
                productInfo.price,
                productInfo.stock
            );
            
            // تمييز البطاقة
            card.classList.add('adding');
            setTimeout(() => {
                card.classList.remove('adding');
            }, 500);
            
            // مسح حقل الباركود
            document.getElementById('barcode-input').value = '';
        }
    });
    
    if (!found) {
        showNotification('المنتج غير موجود أو غير متاح', 'warning');
    }
}

// استخراج معلومات المنتج من البطاقة
function extractProductInfo(card) {
    const nameElement = card.querySelector('h4');
    const priceElement = card.querySelector('.product-price');
    const stockElement = card.querySelector('.product-stock');
    const addButton = card.querySelector('.btn');
    
    // استخراج المعرف من onclick
    const onclickAttr = addButton.getAttribute('onclick');
    const idMatch = onclickAttr.match(/addToInvoice\((\d+),/);
    const id = idMatch ? idMatch[1] : null;
    
    // استخراج السعر
    const priceText = priceElement.textContent;
    const priceMatch = priceText.match(/[\d,]+\.?\d*/);
    const price = priceMatch ? parseFloat(priceMatch[0].replace(',', '')) : 0;
    
    // استخراج المخزون
    const stockText = stockElement.textContent;
    const stockMatch = stockText.match(/\d+/);
    const stock = stockMatch ? parseInt(stockMatch[0]) : 0;
    
    return {
        id: id,
        name: nameElement.textContent,
        price: price,
        stock: stock
    };
}

// إضافة منتج للفاتورة
function addToInvoice(productId, productName, price, stock) {
    // التحقق من وجود المنتج مسبقاً
    const existingItemIndex = invoiceItems.findIndex(item => item.productId == productId);
    
    if (existingItemIndex !== -1) {
        // زيادة الكمية إذا كان المنتج موجود
        const currentQuantity = invoiceItems[existingItemIndex].quantity;
        if (currentQuantity < stock) {
            invoiceItems[existingItemIndex].quantity++;
            updateInvoiceDisplay();
        } else {
            showNotification('لا يمكن إضافة كمية أكثر من المتاح في المخزون', 'warning');
        }
    } else {
        // إضافة منتج جديد
        if (stock > 0) {
            const newItem = {
                id: ++itemCounter,
                productId: productId,
                name: productName,
                price: price,
                quantity: 1,
                stock: stock
            };
            
            invoiceItems.push(newItem);
            updateInvoiceDisplay();
            
            showNotification(`تم إضافة ${productName} للفاتورة`, 'success');
        } else {
            showNotification('المنتج غير متاح في المخزون', 'warning');
        }
    }
}

// تحديث عرض الفاتورة
function updateInvoiceDisplay() {
    const invoiceItemsContainer = document.getElementById('invoice-items');
    const emptyInvoice = document.getElementById('empty-invoice');
    
    if (invoiceItems.length === 0) {
        invoiceItemsContainer.innerHTML = '';
        emptyInvoice.style.display = 'block';
    } else {
        emptyInvoice.style.display = 'none';
        
        let html = '';
        invoiceItems.forEach(item => {
            const total = item.quantity * item.price;
            html += `
                <tr class="invoice-item-new" data-item-id="${item.id}">
                    <td>
                        ${item.name}
                        <input type="hidden" name="products[]" value="${item.productId}">
                    </td>
                    <td>
                        <input type="number" name="quantities[]" value="${item.quantity}" 
                               min="1" max="${item.stock}" class="quantity-input"
                               onchange="updateItemQuantity(${item.id}, this.value)">
                        <small class="text-muted">من ${item.stock}</small>
                    </td>
                    <td>
                        <span class="item-price">${formatMoney(item.price)}</span>
                        <input type="hidden" name="prices[]" value="${item.price}">
                    </td>
                    <td>
                        <span class="item-total">${formatMoney(total)}</span>
                    </td>
                    <td>
                        <button type="button" class="remove-item" onclick="removeFromInvoice(${item.id})">
                            🗑️ حذف
                        </button>
                    </td>
                </tr>
            `;
        });
        
        invoiceItemsContainer.innerHTML = html;
    }
    
    updateTotals();
    updateInvoiceState();
}

// تحديث كمية عنصر
function updateItemQuantity(itemId, newQuantity) {
    const item = invoiceItems.find(item => item.id === itemId);
    if (item) {
        const quantity = parseInt(newQuantity);
        
        if (quantity > 0 && quantity <= item.stock) {
            item.quantity = quantity;
            updateInvoiceDisplay();
        } else if (quantity > item.stock) {
            showNotification(`الكمية المطلوبة أكبر من المتاح (${item.stock})`, 'warning');
            // إعادة تعيين القيمة
            const input = document.querySelector(`[data-item-id="${itemId}"] .quantity-input`);
            if (input) {
                input.value = item.quantity;
            }
        } else {
            removeFromInvoice(itemId);
        }
    }
}

// حذف منتج من الفاتورة
function removeFromInvoice(itemId) {
    invoiceItems = invoiceItems.filter(item => item.id !== itemId);
    updateInvoiceDisplay();
    
    showNotification('تم حذف المنتج من الفاتورة', 'info');
}

// تحديث الإجماليات
function updateTotals() {
    const subtotal = invoiceItems.reduce((sum, item) => sum + (item.quantity * item.price), 0);
    const discount = parseFloat(document.getElementById('discount').value) || 0;
    const finalTotal = subtotal - discount;
    const paidAmount = parseFloat(document.getElementById('paid-amount').value) || 0;
    const remaining = finalTotal - paidAmount;
    
    // تحديث العرض
    document.getElementById('subtotal').textContent = formatMoney(subtotal);
    document.getElementById('discount-amount').textContent = formatMoney(discount);
    document.getElementById('final-total').textContent = formatMoney(finalTotal);
    document.getElementById('paid-display').textContent = formatMoney(paidAmount);
    document.getElementById('remaining-amount').textContent = formatMoney(remaining);
    
    // تلوين المبلغ المتبقي
    const remainingElement = document.getElementById('remaining-amount');
    if (remaining > 0) {
        remainingElement.style.color = '#dc3545';
    } else if (remaining < 0) {
        remainingElement.style.color = '#28a745';
    } else {
        remainingElement.style.color = '#6c757d';
    }
}

// تحديث حالة الفاتورة
function updateInvoiceState() {
    const saveButton = document.getElementById('save-invoice');
    
    if (invoiceItems.length > 0) {
        saveButton.disabled = false;
        saveButton.classList.remove('btn-secondary');
        saveButton.classList.add('btn-success');
    } else {
        saveButton.disabled = true;
        saveButton.classList.remove('btn-success');
        saveButton.classList.add('btn-secondary');
    }
}

// مسح الفاتورة
function clearInvoice() {
    if (invoiceItems.length > 0) {
        if (confirm('هل أنت متأكد من مسح الفاتورة؟')) {
            invoiceItems = [];
            itemCounter = 0;
            
            // مسح النموذج
            document.getElementById('sales-form').reset();
            document.getElementById('customer-select').value = '';
            document.getElementById('customer-info').style.display = 'none';
            
            updateInvoiceDisplay();
            
            showNotification('تم مسح الفاتورة', 'info');
        }
    }
}

// التحقق من صحة الفاتورة
function validateInvoice() {
    if (invoiceItems.length === 0) {
        showNotification('يجب إضافة منتج واحد على الأقل', 'danger');
        return false;
    }
    
    const finalTotal = parseFloat(document.getElementById('final-total').textContent.replace(/[^\d.-]/g, ''));
    if (finalTotal < 0) {
        showNotification('إجمالي الفاتورة لا يمكن أن يكون سالباً', 'danger');
        return false;
    }
    
    return true;
}

// تحميل معلومات العميل
function loadCustomerInfo() {
    const customerSelect = document.getElementById('customer-select');
    const customerInfo = document.getElementById('customer-info');
    
    if (customerSelect.value) {
        const selectedOption = customerSelect.options[customerSelect.selectedIndex];
        const balance = parseFloat(selectedOption.dataset.balance) || 0;
        const phone = selectedOption.dataset.phone || '-';
        
        document.getElementById('customer-balance').textContent = formatMoney(balance);
        document.getElementById('customer-phone').textContent = phone;
        
        customerInfo.style.display = 'block';
        
        // تلوين الرصيد
        const balanceElement = document.getElementById('customer-balance');
        if (balance > 0) {
            balanceElement.className = 'customer-balance-negative';
        } else if (balance < 0) {
            balanceElement.className = 'customer-balance-positive';
        } else {
            balanceElement.className = '';
        }
    } else {
        customerInfo.style.display = 'none';
    }
}

// معاينة الفاتورة
function previewInvoice() {
    if (invoiceItems.length === 0) {
        showNotification('لا توجد عناصر للمعاينة', 'warning');
        return;
    }
    
    // إنشاء نافذة معاينة
    const previewWindow = window.open('', '_blank', 'width=800,height=600');
    const previewContent = generateInvoicePreview();
    
    previewWindow.document.write(previewContent);
    previewWindow.document.close();
}

// إنشاء محتوى معاينة الفاتورة
function generateInvoicePreview() {
    const customerSelect = document.getElementById('customer-select');
    const customerName = customerSelect.value ? 
        customerSelect.options[customerSelect.selectedIndex].text : 'عميل نقدي';
    
    const subtotal = invoiceItems.reduce((sum, item) => sum + (item.quantity * item.price), 0);
    const discount = parseFloat(document.getElementById('discount').value) || 0;
    const finalTotal = subtotal - discount;
    
    let itemsHtml = '';
    invoiceItems.forEach(item => {
        const total = item.quantity * item.price;
        itemsHtml += `
            <tr>
                <td>${item.name}</td>
                <td>${item.quantity}</td>
                <td>${formatMoney(item.price)}</td>
                <td>${formatMoney(total)}</td>
            </tr>
        `;
    });
    
    return `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>معاينة الفاتورة</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .invoice-header { text-align: center; margin-bottom: 30px; }
                .invoice-info { margin-bottom: 20px; }
                table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                th { background-color: #f2f2f2; }
                .totals { text-align: left; }
                .total-row { display: flex; justify-content: space-between; margin: 5px 0; }
                .final-total { font-weight: bold; font-size: 1.2em; }
            </style>
        </head>
        <body>
            <div class="invoice-header">
                <h1>فاتورة مبيعات</h1>
                <p>التاريخ: ${new Date().toLocaleDateString('ar-SA')}</p>
            </div>
            
            <div class="invoice-info">
                <p><strong>العميل:</strong> ${customerName}</p>
            </div>
            
            <table>
                <thead>
                    <tr>
                        <th>المنتج</th>
                        <th>الكمية</th>
                        <th>السعر</th>
                        <th>الإجمالي</th>
                    </tr>
                </thead>
                <tbody>
                    ${itemsHtml}
                </tbody>
            </table>
            
            <div class="totals">
                <div class="total-row">
                    <span>المجموع الفرعي:</span>
                    <span>${formatMoney(subtotal)}</span>
                </div>
                <div class="total-row">
                    <span>الخصم:</span>
                    <span>${formatMoney(discount)}</span>
                </div>
                <div class="total-row final-total">
                    <span>الإجمالي النهائي:</span>
                    <span>${formatMoney(finalTotal)}</span>
                </div>
            </div>
        </body>
        </html>
    `;
}

// معالجة إدخال الباركود
function handleBarcodeInput(event) {
    if (event.key === 'Enter') {
        event.preventDefault();
        searchByBarcode();
    }
}

// تنسيق المبلغ
function formatMoney(amount) {
    return parseFloat(amount).toLocaleString('ar-SA', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }) + ' ريال';
}
