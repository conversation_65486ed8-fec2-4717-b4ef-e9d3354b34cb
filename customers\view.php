<?php
session_start();

require_once '../config/config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول
redirectToLogin();

$customer_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$customer_id) {
    header('Location: index.php');
    exit();
}

try {
    $db = new Database();
    
    // الحصول على بيانات العميل
    $customer = $db->fetch("SELECT * FROM customers WHERE id = ? AND is_active = 1", [$customer_id]);
    
    if (!$customer) {
        throw new Exception('العميل غير موجود');
    }
    
    // الحصول على مبيعات العميل
    $sales = $db->fetchAll("
        SELECT s.*, u.username as user_name
        FROM sales s 
        LEFT JOIN users u ON s.user_id = u.id
        WHERE s.customer_id = ? 
        ORDER BY s.created_at DESC 
        LIMIT 20
    ", [$customer_id]);
    
    // الحصول على طلبات الصيانة
    $maintenance = $db->fetchAll("
        SELECT m.*, u.username as user_name
        FROM maintenance_requests m 
        LEFT JOIN users u ON m.user_id = u.id
        WHERE m.customer_id = ? 
        ORDER BY m.created_at DESC 
        LIMIT 20
    ", [$customer_id]);
    
    // الحصول على المدفوعات
    $payments = $db->fetchAll("
        SELECT p.*, u.username as user_name
        FROM customer_payments p 
        LEFT JOIN users u ON p.user_id = u.id
        WHERE p.customer_id = ? 
        ORDER BY p.created_at DESC 
        LIMIT 20
    ", [$customer_id]);
    
    // إحصائيات العميل
    $stats = [
        'total_sales' => $db->fetch("SELECT COUNT(*) as count, COALESCE(SUM(total_amount), 0) as total FROM sales WHERE customer_id = ?", [$customer_id]),
        'total_maintenance' => $db->fetch("SELECT COUNT(*) as count, COALESCE(SUM(actual_cost), 0) as total FROM maintenance_requests WHERE customer_id = ? AND actual_cost IS NOT NULL", [$customer_id]),
        'total_payments' => $db->fetch("SELECT COALESCE(SUM(CASE WHEN payment_type = 'payment' THEN amount ELSE -amount END), 0) as total FROM customer_payments WHERE customer_id = ?", [$customer_id]),
        'pending_maintenance' => $db->fetch("SELECT COUNT(*) as count FROM maintenance_requests WHERE customer_id = ? AND status IN ('pending', 'in_progress')", [$customer_id])['count']
    ];
    
} catch (Exception $e) {
    $error = $e->getMessage();
    $customer = null;
    $sales = [];
    $maintenance = [];
    $payments = [];
    $stats = ['total_sales' => ['count' => 0, 'total' => 0], 'total_maintenance' => ['count' => 0, 'total' => 0], 'total_payments' => ['total' => 0], 'pending_maintenance' => 0];
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل العميل - <?php echo $customer ? htmlspecialchars($customer['name']) : 'غير موجود'; ?></title>
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="customers.css">
    <style>
        .customer-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .customer-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .info-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .info-icon {
            font-size: 1.2rem;
            opacity: 0.8;
        }
        
        .tabs {
            display: flex;
            border-bottom: 2px solid #f0f0f0;
            margin-bottom: 20px;
        }
        
        .tab {
            padding: 15px 25px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            color: #666;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }
        
        .tab.active {
            color: #667eea;
            border-bottom-color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .transaction-item {
            background: white;
            border: 1px solid #f0f0f0;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            transition: box-shadow 0.3s;
        }
        
        .transaction-item:hover {
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .transaction-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .transaction-title {
            font-weight: 600;
            color: #333;
        }
        
        .transaction-amount {
            font-weight: bold;
            font-size: 1.1rem;
        }
        
        .transaction-details {
            color: #666;
            font-size: 0.9rem;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .status-completed { background: #d4edda; color: #155724; }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-in-progress { background: #d1ecf1; color: #0c5460; }
        .status-failed { background: #f8d7da; color: #721c24; }
        .status-delivered { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <?php include '../includes/header.php'; ?>
    
    <div class="container">
        <?php if (isset($error)): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
            <div class="text-center">
                <a href="index.php" class="btn btn-primary">العودة لقائمة العملاء</a>
            </div>
        <?php else: ?>
            <!-- رأس العميل -->
            <div class="customer-header">
                <div style="display: flex; justify-content: space-between; align-items: start;">
                    <div>
                        <h1>👤 <?php echo htmlspecialchars($customer['name']); ?></h1>
                        <p>عميل منذ <?php echo formatDate($customer['created_at'], 'd/m/Y'); ?></p>
                    </div>
                    <div class="action-buttons">
                        <a href="edit.php?id=<?php echo $customer['id']; ?>" class="btn btn-warning">
                            ✏️ تعديل البيانات
                        </a>
                        <a href="payments.php?customer_id=<?php echo $customer['id']; ?>" class="btn btn-success">
                            💰 إدارة المدفوعات
                        </a>
                        <a href="../sales/?customer_id=<?php echo $customer['id']; ?>" class="btn btn-info">
                            🛒 مبيعة جديدة
                        </a>
                    </div>
                </div>
                
                <div class="customer-info">
                    <div class="info-item">
                        <span class="info-icon">📞</span>
                        <span><?php echo $customer['phone'] ?: 'غير محدد'; ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-icon">📧</span>
                        <span><?php echo $customer['email'] ?: 'غير محدد'; ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-icon">📍</span>
                        <span><?php echo $customer['address'] ?: 'غير محدد'; ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-icon">💰</span>
                        <span class="<?php echo $customer['balance'] > 0 ? 'text-danger' : ($customer['balance'] < 0 ? 'text-success' : ''); ?>">
                            الرصيد: <?php echo formatMoney($customer['balance']); ?>
                        </span>
                    </div>
                </div>
            </div>
            
            <!-- إحصائيات العميل -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">🛒</div>
                    <div class="stat-info">
                        <div class="stat-number"><?php echo $stats['total_sales']['count']; ?></div>
                        <div class="stat-label">إجمالي المبيعات</div>
                        <div class="stat-value"><?php echo formatMoney($stats['total_sales']['total']); ?></div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">🔧</div>
                    <div class="stat-info">
                        <div class="stat-number"><?php echo $stats['total_maintenance']['count']; ?></div>
                        <div class="stat-label">طلبات الصيانة</div>
                        <div class="stat-value"><?php echo formatMoney($stats['total_maintenance']['total']); ?></div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">💳</div>
                    <div class="stat-info">
                        <div class="stat-number"><?php echo formatMoney($stats['total_payments']['total']); ?></div>
                        <div class="stat-label">إجمالي المدفوعات</div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">⏳</div>
                    <div class="stat-info">
                        <div class="stat-number"><?php echo $stats['pending_maintenance']; ?></div>
                        <div class="stat-label">صيانة معلقة</div>
                    </div>
                </div>
            </div>
            
            <!-- التبويبات -->
            <div class="card">
                <div class="tabs">
                    <button class="tab active" onclick="showTab('sales')">المبيعات (<?php echo count($sales); ?>)</button>
                    <button class="tab" onclick="showTab('maintenance')">الصيانة (<?php echo count($maintenance); ?>)</button>
                    <button class="tab" onclick="showTab('payments')">المدفوعات (<?php echo count($payments); ?>)</button>
                </div>
                
                <!-- تبويب المبيعات -->
                <div id="sales-tab" class="tab-content active">
                    <?php if (empty($sales)): ?>
                        <div class="empty-state">
                            <div class="empty-icon">🛒</div>
                            <h3>لا توجد مبيعات</h3>
                            <p>لم يقم هذا العميل بأي عمليات شراء بعد</p>
                            <a href="../sales/?customer_id=<?php echo $customer['id']; ?>" class="btn btn-primary">
                                إضافة مبيعة جديدة
                            </a>
                        </div>
                    <?php else: ?>
                        <?php foreach ($sales as $sale): ?>
                            <div class="transaction-item">
                                <div class="transaction-header">
                                    <div class="transaction-title">
                                        فاتورة #<?php echo $sale['invoice_number']; ?>
                                    </div>
                                    <div class="transaction-amount text-success">
                                        <?php echo formatMoney($sale['total_amount']); ?>
                                    </div>
                                </div>
                                <div class="transaction-details">
                                    <div>التاريخ: <?php echo formatDate($sale['created_at'], 'd/m/Y H:i'); ?></div>
                                    <div>الموظف: <?php echo htmlspecialchars($sale['user_name']); ?></div>
                                    <div>المدفوع: <?php echo formatMoney($sale['paid_amount']); ?></div>
                                    <?php if ($sale['total_amount'] - $sale['paid_amount'] > 0): ?>
                                        <div class="text-danger">المتبقي: <?php echo formatMoney($sale['total_amount'] - $sale['paid_amount']); ?></div>
                                    <?php endif; ?>
                                    <div class="mt-2">
                                        <a href="../sales/view.php?id=<?php echo $sale['id']; ?>" class="btn btn-sm btn-info">عرض التفاصيل</a>
                                        <a href="../sales/print.php?id=<?php echo $sale['id']; ?>" class="btn btn-sm btn-secondary" target="_blank">طباعة</a>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
                
                <!-- تبويب الصيانة -->
                <div id="maintenance-tab" class="tab-content">
                    <?php if (empty($maintenance)): ?>
                        <div class="empty-state">
                            <div class="empty-icon">🔧</div>
                            <h3>لا توجد طلبات صيانة</h3>
                            <p>لم يقم هذا العميل بأي طلبات صيانة بعد</p>
                            <a href="../maintenance/?customer_id=<?php echo $customer['id']; ?>" class="btn btn-primary">
                                إضافة طلب صيانة
                            </a>
                        </div>
                    <?php else: ?>
                        <?php foreach ($maintenance as $request): ?>
                            <div class="transaction-item">
                                <div class="transaction-header">
                                    <div class="transaction-title">
                                        <?php echo htmlspecialchars($request['device_name']); ?>
                                        <span class="status-badge status-<?php echo $request['status']; ?>">
                                            <?php
                                            $status_labels = [
                                                'pending' => 'معلق',
                                                'in_progress' => 'قيد التنفيذ',
                                                'completed' => 'مكتمل',
                                                'failed' => 'فاشل',
                                                'delivered' => 'مسلم'
                                            ];
                                            echo $status_labels[$request['status']] ?? $request['status'];
                                            ?>
                                        </span>
                                    </div>
                                    <div class="transaction-amount text-warning">
                                        <?php echo $request['actual_cost'] ? formatMoney($request['actual_cost']) : formatMoney($request['estimated_cost']); ?>
                                    </div>
                                </div>
                                <div class="transaction-details">
                                    <div>رقم الطلب: <?php echo $request['maintenance_number']; ?></div>
                                    <div>التاريخ: <?php echo formatDate($request['created_at'], 'd/m/Y H:i'); ?></div>
                                    <div>الموظف: <?php echo htmlspecialchars($request['user_name']); ?></div>
                                    <div>العطل: <?php echo htmlspecialchars($request['problem_description']); ?></div>
                                    <div class="mt-2">
                                        <a href="../maintenance/view.php?id=<?php echo $request['id']; ?>" class="btn btn-sm btn-info">عرض التفاصيل</a>
                                        <a href="../maintenance/print.php?id=<?php echo $request['id']; ?>" class="btn btn-sm btn-secondary" target="_blank">طباعة</a>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
                
                <!-- تبويب المدفوعات -->
                <div id="payments-tab" class="tab-content">
                    <?php if (empty($payments)): ?>
                        <div class="empty-state">
                            <div class="empty-icon">💳</div>
                            <h3>لا توجد مدفوعات</h3>
                            <p>لم يتم تسجيل أي مدفوعات لهذا العميل</p>
                            <a href="payments.php?customer_id=<?php echo $customer['id']; ?>" class="btn btn-primary">
                                إضافة دفعة جديدة
                            </a>
                        </div>
                    <?php else: ?>
                        <?php foreach ($payments as $payment): ?>
                            <div class="transaction-item">
                                <div class="transaction-header">
                                    <div class="transaction-title">
                                        <?php echo $payment['payment_type'] == 'payment' ? 'دفعة' : 'استرداد'; ?>
                                        <?php if ($payment['reference_type'] != 'manual'): ?>
                                            - <?php echo $payment['reference_type'] == 'sale' ? 'مبيعة' : 'صيانة'; ?>
                                        <?php endif; ?>
                                    </div>
                                    <div class="transaction-amount <?php echo $payment['payment_type'] == 'payment' ? 'text-success' : 'text-danger'; ?>">
                                        <?php echo ($payment['payment_type'] == 'refund' ? '-' : '') . formatMoney($payment['amount']); ?>
                                    </div>
                                </div>
                                <div class="transaction-details">
                                    <div>التاريخ: <?php echo formatDate($payment['created_at'], 'd/m/Y H:i'); ?></div>
                                    <div>الموظف: <?php echo htmlspecialchars($payment['user_name']); ?></div>
                                    <?php if (!empty($payment['notes'])): ?>
                                        <div>ملاحظات: <?php echo htmlspecialchars($payment['notes']); ?></div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="text-center mt-3">
                <a href="index.php" class="btn btn-secondary">← العودة لقائمة العملاء</a>
            </div>
        <?php endif; ?>
    </div>
    
    <?php include '../includes/footer.php'; ?>
    
    <script src="../assets/js/main.js"></script>
    <script>
        function showTab(tabName) {
            // إخفاء جميع التبويبات
            const tabs = document.querySelectorAll('.tab');
            const contents = document.querySelectorAll('.tab-content');
            
            tabs.forEach(tab => tab.classList.remove('active'));
            contents.forEach(content => content.classList.remove('active'));
            
            // إظهار التبويب المحدد
            event.target.classList.add('active');
            document.getElementById(tabName + '-tab').classList.add('active');
        }
    </script>
</body>
</html>
