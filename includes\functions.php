<?php
// ملف الدوال المشتركة

// بدء الجلسة إذا لم تكن مبدوءة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// دالة التحقق من تسجيل الدخول
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

// دالة إعادة التوجيه إلى صفحة تسجيل الدخول
function redirectToLogin() {
    if (!isLoggedIn()) {
        header('Location: ../login.php');
        exit();
    }
}

// دالة تنظيف البيانات
function sanitize($data) {
    return htmlspecialchars(strip_tags(trim($data)));
}

// دالة التحقق من صحة البريد الإلكتروني
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

// دالة التحقق من صحة رقم الهاتف
function validatePhone($phone) {
    return preg_match('/^[0-9+\-\s()]+$/', $phone);
}

// دالة تنسيق التاريخ
function formatDate($date, $format = 'Y-m-d H:i:s') {
    return date($format, strtotime($date));
}

// دالة تنسيق المبلغ
function formatMoney($amount, $currency = null) {
    if ($currency === null) {
        // الحصول على العملة من الإعدادات
        try {
            $db = new Database();
            $conn = $db->getConnection();
            $stmt = $conn->prepare("SELECT setting_value FROM settings WHERE setting_key = 'currency_symbol'");
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $currency = $result ? $result['setting_value'] : 'د.ع';
        } catch (Exception $e) {
            $currency = 'د.ع';
        }
    }

    return number_format($amount, 0) . ' ' . $currency;
}

// دالة إنشاء رقم فاتورة فريد
function generateInvoiceNumber() {
    return 'INV-' . date('Ymd') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
}

// دالة إنشاء رقم صيانة فريد
function generateMaintenanceNumber() {
    return 'MNT-' . date('Ymd') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
}

// دالة تسجيل العمليات في السجل
function logActivity($action, $details = '', $user_id = null) {
    if (!$user_id && isset($_SESSION['user_id'])) {
        $user_id = $_SESSION['user_id'];
    }
    
    try {
        $db = new Database();
        $conn = $db->getConnection();
        
        $data = [
            'user_id' => $user_id,
            'action' => $action,
            'details' => $details,
            'ip_address' => $_SERVER['REMOTE_ADDR'],
            'user_agent' => $_SERVER['HTTP_USER_AGENT'],
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        $db->insert('activity_logs', $data);
    } catch (Exception $e) {
        error_log("خطأ في تسجيل النشاط: " . $e->getMessage());
    }
}

// دالة إرسال إشعار
function sendNotification($type, $title, $message, $user_id = null) {
    try {
        $db = new Database();
        $conn = $db->getConnection();
        
        $data = [
            'user_id' => $user_id,
            'type' => $type,
            'title' => $title,
            'message' => $message,
            'is_read' => 0,
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        return $db->insert('notifications', $data);
    } catch (Exception $e) {
        error_log("خطأ في إرسال الإشعار: " . $e->getMessage());
        return false;
    }
}

// دالة التحقق من المخزون المنخفض
function checkLowStock() {
    try {
        $db = new Database();
        $conn = $db->getConnection();
        
        $sql = "SELECT * FROM products WHERE quantity <= min_quantity AND is_active = 1";
        $lowStockProducts = $db->fetchAll($sql);
        
        foreach ($lowStockProducts as $product) {
            $message = "المنتج '{$product['name']}' وصل إلى الحد الأدنى للمخزون. الكمية المتاحة: {$product['quantity']}";
            sendNotification('low_stock', 'تنبيه مخزون منخفض', $message);
        }
        
        return $lowStockProducts;
    } catch (Exception $e) {
        error_log("خطأ في فحص المخزون: " . $e->getMessage());
        return [];
    }
}

// دالة التحقق من طلبات الصيانة المتأخرة
function checkOverdueMaintenanceRequests() {
    try {
        $db = new Database();
        $conn = $db->getConnection();
        
        $sql = "SELECT * FROM maintenance_requests 
                WHERE status = 'pending' 
                AND DATEDIFF(NOW(), created_at) > 5";
        $overdueRequests = $db->fetchAll($sql);
        
        foreach ($overdueRequests as $request) {
            $days = floor((time() - strtotime($request['created_at'])) / (60 * 60 * 24));
            $message = "طلب الصيانة رقم '{$request['maintenance_number']}' متأخر منذ {$days} أيام";
            sendNotification('overdue_maintenance', 'تنبيه صيانة متأخرة', $message);
        }
        
        return $overdueRequests;
    } catch (Exception $e) {
        error_log("خطأ في فحص الصيانة المتأخرة: " . $e->getMessage());
        return [];
    }
}

// دالة رفع الملفات
function uploadFile($file, $uploadDir = '../uploads/') {
    if (!isset($file['error']) || is_array($file['error'])) {
        return ['success' => false, 'message' => 'خطأ في رفع الملف'];
    }
    
    if ($file['error'] !== UPLOAD_ERR_OK) {
        return ['success' => false, 'message' => 'خطأ في رفع الملف'];
    }
    
    if ($file['size'] > 5000000) { // 5MB
        return ['success' => false, 'message' => 'حجم الملف كبير جداً'];
    }
    
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'];
    if (!in_array($file['type'], $allowedTypes)) {
        return ['success' => false, 'message' => 'نوع الملف غير مسموح'];
    }
    
    $fileName = uniqid() . '_' . basename($file['name']);
    $uploadPath = $uploadDir . $fileName;
    
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
        return ['success' => true, 'filename' => $fileName, 'path' => $uploadPath];
    } else {
        return ['success' => false, 'message' => 'فشل في رفع الملف'];
    }
}

// دالة إنشاء نسخة احتياطية
function createBackup() {
    try {
        include '../config/config.php';
        
        $backupFile = '../backups/backup_' . date('Y-m-d_H-i-s') . '.sql';
        $command = "mysqldump --user=" . DB_USER . " --password=" . DB_PASS . " --host=" . DB_HOST . " " . DB_NAME . " > " . $backupFile;
        
        exec($command, $output, $return_var);
        
        if ($return_var === 0) {
            return ['success' => true, 'file' => $backupFile];
        } else {
            return ['success' => false, 'message' => 'فشل في إنشاء النسخة الاحتياطية'];
        }
    } catch (Exception $e) {
        return ['success' => false, 'message' => $e->getMessage()];
    }
}

// دالة تشفير كلمة المرور
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

// دالة التحقق من كلمة المرور
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

// دالة إنشاء رمز مميز
function generateToken($length = 32) {
    return bin2hex(random_bytes($length));
}
?>
